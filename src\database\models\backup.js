const mongoose = require('mongoose');

const backupSchema = new mongoose.Schema({
    backupId: { type: String, required: true, unique: true },
    guildId: { type: String, required: true },
    userId: { type: String, required: true }, // New field for user ID
    data: { type: Object, required: true }, // Store all backup data here
    timestamp: { type: Date, default: Date.now },
    messageCount: { type: Number, default: 0 },
    intervalSettings: { // For scheduled backups
        enabled: { type: Boolean, default: false },
        interval: { type: String }, // e.g., "24h", "4h"
        lastBackup: { type: Date }
    }
});

module.exports = mongoose.model('Backup', backupSchema);