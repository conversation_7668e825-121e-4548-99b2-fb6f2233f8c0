const Discord = require('discord.js');
const { SlashCommandBuilder } = require('@discordjs/builders');

const Schema = require("../../database/models/stats");

module.exports = {
    data: new SlashCommandBuilder()
        .setName('setchannelname')
        .setDescription('Set the channel name template for stat channels.')
        .addStringOption(option =>
            option.setName('name')
                .setDescription('The channel name template. Type HELP for options.')
                .setRequired(true)),
    async execute(client, interaction, args) {
        const perms = await client.checkUserPerms({
            flags: [Discord.PermissionsBitField.Flags.ManageChannels],
            perms: [Discord.PermissionsBitField.Flags.ManageChannels]
        }, interaction)

        if (perms == false) return;

        const name = interaction.options.getString('name');

        if (name.toUpperCase() == "HELP") {
            return client.embed({
                title: `ℹ️・Channel name options`,
                desc: `These are the channel name options: \n\n                \`{emoji}\` - Channel emoji\n                \`{name}\` - Channel name`,
                type: 'editreply'
            }, interaction)
        }

        Schema.findOne({ Guild: interaction.guild.id }, async (err, data) => {
            if (data) {
                data.ChannelTemplate = name
                data.save();
            }
            else {
                new Schema({
                    Guild: interaction.guild.id,
                    ChannelTemplate: name
                }).save();
            }

            client.succNormal({
                text: `The channel name has been set successfully`,
                fields: [
                    {
                        name: `💬┆Name`,
                        value: `${name}`,
                        inline: true
                    },
                ],
                type: 'editreply'
            }, interaction)
        })
    },
};

 