const { CommandInteraction, Client } = require('discord.js');
const { SlashCommandBuilder } = require('discord.js');
const Discord = require('discord.js');

const model = require('../../database/models/badge');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('admin')
        .setDescription('Commands for the Bot owner')
        .addSubcommand(subcommand =>
            subcommand
                .setName('help')
                .setDescription('Get information about the owner category commands')
        )
        .addSubcommand(subcommand =>
            subcommand
                .setName('eval')
                .setDescription('Get the result of a piece of code')
                .addStringOption(option => option.setName('code').setDescription('Your code').setRequired(true))
        )
        .addSubcommand(subcommand =>
            subcommand
                .setName('badge')
                .setDescription('Manage the bot badges')
                .addBooleanOption(option => option.setName('new').setDescription('Select a boolean').setRequired(true))
                .addUserOption(option => option.setName('user').setDescription('Select a user').setRequired(true))
                .addStringOption(option => option.setName('badge').setDescription('Choose your badge').setRequired(true))
        )
        .addSubcommand(subcommand =>
            subcommand
                .setName('ban')
                .setDescription('Ban a user or guild from the bot')
                .addStringOption(option =>
                    option.setName('type')
                        .setDescription('Ban a user or a guild')
                        .setRequired(true)
                        .addChoices(
                            { name: 'User', value: 'user' },
                            { name: 'Guild', value: 'guild' }
                        )
                )
                .addStringOption(option =>
                    option.setName('id')
                        .setDescription('User ID or Guild ID to ban')
                        .setRequired(true)
                )
                .addStringOption(option =>
                    option.setName('reason')
                        .setDescription('Reason for the ban')
                        .setRequired(false)
                )
        )
        .addSubcommand(subcommand =>
            subcommand
                .setName('credits')
                .setDescription('Manage the bot credits')
                .addStringOption(option =>
                    option.setName('type')
                        .setDescription('The type of credits')
                        .setRequired(true)
                        .addChoices(
                            { name: 'Add', value: 'add' },
                            { name: 'Remove', value: 'remove' }
                        )
                )
                .addUserOption(option => option.setName('user').setDescription('Select a user').setRequired(true))
                .addNumberOption(option => option.setName('amount').setDescription('Amount of credits').setRequired(true))
        )
        .addSubcommand(subcommand =>
            subcommand
                .setName('args')
                .setDescription('Post preset messages')
                .addStringOption(option =>
                    option.setName('message')
                        .setDescription('Select a message')
                        .setRequired(true)
                        .addChoices(
                            { name: 'Information', value: 'information' },
                            { name: 'Rules', value: 'rules' },
                            { name: 'Applications', value: 'applications' },
                            { name: 'Booster perks', value: 'boosterperks' },
                            { name: 'Links', value: 'links' },
                            { name: 'Rewards', value: 'rewards' },
                            { name: 'Our bots', value: 'ourbots' }
                        )
                )
        )
        .addSubcommand(subcommand =>
            subcommand
                .setName('servers')
                .setDescription('See all servers from this shard')
        )
    ,

    /** 
     * @param {Client} client
     * @param {CommandInteraction} interaction
     * @param {String[]} args
     */

    run: async (client, interaction, args) => {
        const owners = process.env.OWNERS ? process.env.OWNERS.split(',') : [];
        if (!owners.includes(interaction.user.id)) {
            return client.errNormal({
                error: 'Only Bot owners are allowed to do this',
                type: 'ephemeral'
            }, interaction)
        }
        await interaction.deferReply({ fetchReply: true });
        client.loadSubcommands(client, interaction, args);

        if (interaction.options.getSubcommand() === 'ban') {
            const type = interaction.options.getString('type');
            const id = interaction.options.getString('id');
            const reason = interaction.options.getString('reason') || 'No reason provided';

            const logChannelId = client.config.logging.banChannel; // Set this in your config
            const logChannel = client.channels.cache.get(logChannelId);

            if (type === 'user') {
                // Save to user ban database
                const banSchema = require('../../database/models/userBans');
                await banSchema.updateOne(
                    { User: id },
                    { $set: { User: id, Reason: reason } },
                    { upsert: true }
                );
                client.succNormal({
                    text: `User <@${id}> has been banned from using the bot.`, 
                    type: 'editreply'
                }, interaction);

                // Log embed
                if (logChannel) {
                    const embed = new Discord.EmbedBuilder()
                        .setTitle('🚫・User Banned')
                        .addFields(
                            { name: 'User ID', value: id, inline: true },
                            { name: 'Reason', value: reason, inline: true },
                            { name: 'Banned By', value: `${interaction.user.tag} (${interaction.user.id})`, inline: false }
                        )
                        .setColor(client.config.colors.error)
                        .setTimestamp();
                    logChannel.send({ embeds: [embed] });
                }
            } else if (type === 'guild') {
                // Save to guild ban database
                const banSchema = require('../../database/models/guildBans');
                await banSchema.updateOne(
                    { Guild: id },
                    { $set: { Guild: id, Reason: reason } },
                    { upsert: true }
                );
                client.succNormal({
                    text: `Guild \`${id}\` has been banned from using the bot.`, 
                    type: 'editreply'
                }, interaction);

                // Log embed
                if (logChannel) {
                    const embed = new Discord.EmbedBuilder()
                        .setTitle('🚫・Guild Banned')
                        .addFields(
                            { name: 'Guild ID', value: id, inline: true },
                            { name: 'Reason', value: reason, inline: true },
                            { name: 'Banned By', value: `${interaction.user.tag} (${interaction.user.id})`, inline: false }
                        )
                        .setColor(client.config.colors.error)
                        .setTimestamp();
                    logChannel.send({ embeds: [embed] });
                }
            }
            return;
        }
    },
};