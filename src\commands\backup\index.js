const { SlashCommandBuilder, MessageFlags, ChannelType, PermissionFlagsBits, EmbedBuilder, ActionRowBuilder, ButtonBuilder, ButtonStyle } = require('discord.js');
const Backup = require('../../database/models/backup');
const IntervalBackup = require('../../database/models/intervalBackup');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('backup')
        .setDescription('Manage server backups.')
        .addSubcommand(subcommand =>
            subcommand
                .setName('create')
                .setDescription('Creates a backup of the server (channels, roles, settings).')
                .addIntegerOption(option =>
                    option.setName('message_count')
                        .setDescription('Number of messages to backup per channel (Premium only).')
                        .setRequired(false)
                )
        )
        .addSubcommand(subcommand =>
            subcommand
                .setName('list')
                .setDescription('Shows all backups you’ve created, including scheduled (“interval”) backups.')
        )
        .addSubcommand(subcommand =>
            subcommand
                .setName('load')
                .setDescription('Restores a backup by its ID.')
                .addStringOption(option =>
                    option.setName('backup_id')
                        .setDescription('The ID of the backup to restore.')
                        .setRequired(true)
                )
                
                .addBooleanOption(option =>
                    option.setName('delete_channels')
                        .setDescription('Whether to delete existing channels before loading the backup.')
                        .setRequired(false)
                )
                .addBooleanOption(option =>
                    option.setName('delete_roles')
                        .setDescription('Whether to delete existing roles before loading the backup.')
                        .setRequired(false)
                )
        )
        .addSubcommand(subcommand =>
            subcommand
                .setName('delete')
                .setDescription('Permanently deletes a backup by ID.')
                .addStringOption(option =>
                    option.setName('backup_id')
                        .setDescription('The ID of the backup to delete.')
                        .setRequired(true)
                )
        )
        .addSubcommandGroup(group =>
            group
                .setName('interval')
                .setDescription('Manages automated backups.')
                .addSubcommand(subcommand =>
                    subcommand
                        .setName('on')
                        .setDescription('Enables automated backups at regular intervals.')
                        .addStringOption(option =>
                            option.setName('interval')
                                .setDescription('The interval for automated backups (e.g., 24h, 4h for Premium).')
                                .setRequired(true)
                        )
                        .addIntegerOption(option =>
                            option.setName('message_count')
                                .setDescription('Number of messages to backup per channel (Premium only).')
                                .setRequired(false)
                        )
                )
                .addSubcommand(subcommand =>
                    subcommand
                        .setName('show')
                        .setDescription('Displays your current backup schedule and interval.')
                )
        ),
    async execute(interaction) {
        const getPermissionBigInt = (permissionData) => {
            if (typeof permissionData === 'string') {
                return BigInt(permissionData);
            }
            if (typeof permissionData === 'object' && permissionData !== null && typeof permissionData.bitfield !== 'undefined') {
                return BigInt(String(permissionData.bitfield));
            }
            return BigInt(0); // Default to 0 if data is invalid
        };

        const subcommand = interaction.options.getSubcommand();
        const subcommandGroup = interaction.options.getSubcommandGroup();

        if (subcommandGroup === 'interval') {
            if (subcommand === 'on') {
                await interaction.deferReply({ flags: MessageFlags.Ephemeral });
                const interval = interaction.options.getString('interval');
                const messageCount = interaction.options.getInteger('message_count') || 0;

                // Basic validation for interval format (e.g., 24h, 12h, 4h)
                const validIntervals = ['24h', '12h', '4h', '1h']; // Example valid intervals
                if (!validIntervals.includes(interval)) {
                    return await interaction.editReply(`Invalid interval format. Please use one of: ${validIntervals.join(', ')}.`);
                }

                try {
                    await IntervalBackup.findOneAndUpdate(
                        { guildId: interaction.guild.id, userId: interaction.user.id },
                        { interval: interval, messageCount: messageCount, lastBackup: new Date() },
                        { upsert: true, new: true }
                    );
                    await interaction.editReply(`Automated backups enabled for this server with an interval of \`${interval}\` and ${messageCount} messages per channel.`);
                } catch (error) {
                    console.error('Error setting interval backup:', error);
                    await interaction.editReply('Failed to set interval backup. Please try again later.');
                }

            } else if (subcommand === 'show') {
                await interaction.deferReply({ flags: MessageFlags.Ephemeral });

                try {
                    const intervalSettings = await IntervalBackup.findOne({ guildId: interaction.guild.id, userId: interaction.user.id });

                    if (!intervalSettings) {
                        return await interaction.editReply('No automated backup interval is set for this server.');
                    }

                    await interaction.editReply(`Automated backups are set to run every \`${intervalSettings.interval}\` with ${intervalSettings.messageCount} messages per channel. Last backup: ${intervalSettings.lastBackup.toLocaleString()}.`);
                } catch (error) {
                    console.error('Error showing interval backup:', error);
                    await interaction.editReply('Failed to retrieve interval backup settings. Please try again later.');
                }
            }
        } else if (subcommand === 'create') {
            await interaction.deferReply({ flags: MessageFlags.Ephemeral });

            // Initial embed setup for create
            let createProgressEmbed = new EmbedBuilder()
                .setTitle('Creating Backup...')
                .setDescription('Starting backup process.')
                .setColor('Blue')
                .addFields(
                    { name: 'Backing up Channels', value: 'Pending...', inline: true },
                    { name: 'Backing up Roles', value: 'Pending...', inline: true },
                    { name: 'Backing up Messages', value: 'Pending...', inline: true }
                );
            await interaction.editReply({ embeds: [createProgressEmbed] });

            const messageCount = interaction.options.getInteger('message_count') || 0;

            try {
                // --- Backing up Channels ---
                createProgressEmbed.spliceFields(0, 1, { name: 'Backing up Channels', value: 'In Progress...', inline: true });
                await interaction.editReply({ embeds: [createProgressEmbed] });

                const totalChannels = interaction.guild.channels.cache.size;
                let processedChannelCount = 0;
                const channelsData = interaction.guild.channels.cache
                    .sort((a, b) => a.position - b.position)
                    .map(channel => {
                        processedChannelCount++;
                        return ({
                            id: channel.id,
                            name: channel.name,
                            type: channel.type,
                            topic: channel.topic,
                            nsfw: channel.nsfw,
                            parentId: channel.parentId,
                            position: channel.position,
                            permissionOverwrites: channel.permissionOverwrites.cache.map(overwrite => ({
                                id: overwrite.id,
                                type: overwrite.type,
                                allow: overwrite.allow.bitfield.toString(),
                                deny: overwrite.deny.bitfield.toString()
                            }))
                        });
                    });
                createProgressEmbed.spliceFields(0, 1, { name: 'Backing up Channels', value: 'Complete!', inline: true });
                await interaction.editReply({ embeds: [createProgressEmbed] });

                // --- Backing up Roles ---
                createProgressEmbed.spliceFields(1, 1, { name: 'Backing up Roles', value: 'In Progress...', inline: true });
                await interaction.editReply({ embeds: [createProgressEmbed] });

                const totalRoles = interaction.guild.roles.cache.size;
                let processedRoleCount = 0;
                const rolesData = interaction.guild.roles.cache
                    .sort((a, b) => a.position - b.position)
                    .map(role => {
                        processedRoleCount++;
                        return ({
                            id: role.id,
                            name: role.name,
                            color: role.color,
                            hoist: role.hoist,
                            mentionable: role.mentionable,
                            permissions: role.permissions.bitfield.toString(),
                            position: role.position
                        });
                    });
                createProgressEmbed.spliceFields(1, 1, { name: 'Backing up Roles', value: 'Complete!', inline: true });
                await interaction.editReply({ embeds: [createProgressEmbed] });

                // --- Backing up Messages ---
                const messagesData = {};
                if (messageCount > 0) {
                    createProgressEmbed.spliceFields(2, 1, { name: 'Backing up Messages', value: 'In Progress...', inline: true });
                    await interaction.editReply({ embeds: [createProgressEmbed] });

                    const textChannels = interaction.guild.channels.cache.filter(channel => channel.type === ChannelType.GuildText);
                    let processedMessageChannelCount = 0;
                    const totalMessageChannels = textChannels.size;

                    for (const channel of textChannels.values()) {
                        try {
                            const fetchedMessages = await channel.messages.fetch({ limit: Math.min(messageCount, 50) });
                            messagesData[channel.id] = fetchedMessages.map(msg => ({
                                authorId: msg.author.id,
                                content: msg.content,
                                createdAt: msg.createdAt,
                                attachments: msg.attachments.map(att => ({
                                    url: att.url,
                                    name: att.name,
                                    contentType: att.contentType
                                }))
                            })).reverse();
                        } catch (error) {
                            console.error(`Failed to fetch messages from channel ${channel.name} (${channel.id}):`, error);
                        }
                        processedMessageChannelCount++;
                        const progress = Math.floor((processedMessageChannelCount / totalMessageChannels) * 100);
                        createProgressEmbed.spliceFields(2, 1, { name: 'Backing up Messages', value: `${progress}%`, inline: true });
                        await interaction.editReply({ embeds: [createProgressEmbed] });
                    }
                    createProgressEmbed.spliceFields(2, 1, { name: 'Backing up Messages', value: 'Complete!', inline: true });
                    await interaction.editReply({ embeds: [createProgressEmbed] });
                } else {
                    createProgressEmbed.spliceFields(2, 1, { name: 'Backing up Messages', value: 'Skipped (message_count is 0)', inline: true });
                    await interaction.editReply({ embeds: [createProgressEmbed] });
                }

                const backupData = {
                    guildName: interaction.guild.name,
                    guildId: interaction.guild.id,
                    channels: channelsData,
                    roles: rolesData,
                    settings: {},
                    messages: messagesData
                };

                const newBackup = new Backup({
                    backupId: `${interaction.guild.id}-${Date.now()}`,
                    guildId: interaction.guild.id,
                    userId: interaction.user.id,
                    data: backupData,
                    messageCount: messageCount
                });

                await newBackup.save();
                createProgressEmbed.setDescription(`Backup created successfully with ID: \`${newBackup.backupId}\`.`);
                createProgressEmbed.setColor('Green');
                await interaction.editReply({ embeds: [createProgressEmbed] });

            } catch (error) {
                console.error('Error creating backup:', error);
                createProgressEmbed.setDescription('Failed to create backup. An error occurred.');
                createProgressEmbed.setColor('Red');
                await interaction.editReply({ embeds: [createProgressEmbed] });
            }

        } else if (subcommand === 'list') {
            await interaction.deferReply({ flags: MessageFlags.Ephemeral });

            try {
                const backups = await Backup.find({ userId: interaction.user.id });

                if (backups.length === 0) {
                    return await interaction.editReply('No backups found for you.');
                }

                let replyMessage = '**Your Backups:**\n';
                backups.forEach((backup, index) => {
                    replyMessage += `\n${index + 1}. Server: ${backup.data.guildName} | ID: \`${backup.backupId}\``;
                });

                await interaction.editReply(replyMessage);
            } catch (error) {
                console.error('Error listing backups:', error);
                await interaction.editReply('Failed to retrieve backups. Please try again later.\nIf you just created a backup, it might take a moment to appear.');
            }
        } else if (subcommand === 'load') {
            await interaction.deferReply({ flags: MessageFlags.Ephemeral });
            const backupId = interaction.options.getString('backup_id');

            try {
                const backup = await Backup.findOne({ userId: interaction.user.id, backupId: backupId });

                if (!backup) {
                    return await interaction.editReply(`Backup with ID \`${backupId}\` not found or does not belong to you.`);
                }

                // Confirmation step before loading
                const confirmEmbed = new EmbedBuilder()
                    .setTitle('⚠️ Warning: Loading Backup')
                    .setDescription(`Are you sure you want to load backup \`${backup.backupId}\` from server **${backup.data.guildName}** to this server? This will overwrite existing channels, roles, and settings.`)
                    .setColor('Yellow');

                const confirmRow = new ActionRowBuilder()
                    .addComponents(
                        new ButtonBuilder()
                            .setCustomId('confirm_load_backup')
                            .setLabel('Confirm Load')
                            .setStyle(ButtonStyle.Danger),
                        new ButtonBuilder()
                            .setCustomId('cancel_load_backup')
                            .setLabel('Cancel')
                            .setStyle(ButtonStyle.Secondary),
                    );

                const response = await interaction.editReply({
                    embeds: [confirmEmbed],
                    components: [confirmRow],
                    fetchReply: true
                });

                const collector = response.createMessageComponentCollector({
                    filter: i => i.user.id === interaction.user.id,
                    time: 60000
                });

                collector.on('collect', async i => {
                    if (i.customId === 'confirm_load_backup') {
                        // Initial embed for load progress
                        let loadProgressEmbed = new EmbedBuilder()
                            .setTitle('Loading Backup...')
                            .setDescription(`Restoring backup \`${backup.backupId}\` to **${interaction.guild.name}**.`)
                            .setColor('Blue')
                            .addFields(
                                { name: 'Deleted Channels', value: 'Pending...', inline: true },
                                { name: 'Deleted Roles', value: 'Pending...', inline: true },
                                { name: 'Created Roles', value: 'Pending...', inline: true },
                                { name: 'Created Channels', value: 'Pending...', inline: true },
                                { name: 'Restoring Messages', value: 'Pending...', inline: true }
                            );
                        await i.update({ embeds: [loadProgressEmbed], components: [] }); // Update the original message with the progress embed

                        try {
                            const deleteChannels = interaction.options.getBoolean('delete_channels') || false;
                            const deleteRoles = interaction.options.getBoolean('delete_roles') || false;
                            // messageCount option removed, messages will be restored if present in backup.data.messages

                            // --- Delete existing channels ---
                            if (deleteChannels) {
                                loadProgressEmbed.spliceFields(0, 1, { name: 'Deleted Channels', value: 'In Progress...', inline: true });
                                await interaction.editReply({ embeds: [loadProgressEmbed] });

                                await interaction.guild.channels.fetch();
                                const essentialChannelIds = [
                                    interaction.guild.rulesChannelId,
                                    interaction.guild.publicUpdatesChannelId,
                                    interaction.guild.moderationChannelsId,
                                    interaction.channel.id
                                ].filter(Boolean);

                                const deletableChannels = interaction.guild.channels.cache.filter(channel => !essentialChannelIds.includes(channel.id));
                                let deletedChannelCount = 0;
                                const totalDeletableChannels = deletableChannels.size;

                                for (const channel of deletableChannels.values()) {
                                    await channel.delete().catch(error => {
                                        console.error(`Failed to delete channel ${channel.name} (${channel.id}):`, error);
                                    });
                                    deletedChannelCount++;
                                    const progress = Math.floor((deletedChannelCount / totalDeletableChannels) * 100);
                                    loadProgressEmbed.spliceFields(0, 1, { name: 'Deleted Channels', value: `${progress}%`, inline: true });
                                    await interaction.editReply({ embeds: [loadProgressEmbed] });
                                }
                                loadProgressEmbed.spliceFields(0, 1, { name: 'Deleted Channels', value: 'Complete!', inline: true });
                                await interaction.editReply({ embeds: [loadProgressEmbed] });
                            } else {
                                loadProgressEmbed.spliceFields(0, 1, { name: 'Deleted Channels', value: 'Skipped', inline: true });
                                await interaction.editReply({ embeds: [loadProgressEmbed] });
                            }

                            // --- Delete existing roles ---
                            if (deleteRoles) {
                                loadProgressEmbed.spliceFields(1, 1, { name: 'Deleted Roles', value: 'In Progress...', inline: true });
                                await interaction.editReply({ embeds: [loadProgressEmbed] });

                                await interaction.guild.roles.fetch();
                                const deletableRoles = interaction.guild.roles.cache.filter(role => role.name !== '@everyone' && !role.managed);
                                let deletedRoleCount = 0;
                                const totalDeletableRoles = deletableRoles.size;

                                for (const role of deletableRoles.values()) {
                                    await role.delete().catch(console.error);
                                    deletedRoleCount++;
                                    const progress = Math.floor((deletedRoleCount / totalDeletableRoles) * 100);
                                    loadProgressEmbed.spliceFields(1, 1, { name: 'Deleted Roles', value: `${progress}%`, inline: true });
                                    await interaction.editReply({ embeds: [loadProgressEmbed] });
                                }
                                loadProgressEmbed.spliceFields(1, 1, { name: 'Deleted Roles', value: 'Complete!', inline: true });
                                await interaction.editReply({ embeds: [loadProgressEmbed] });
                            } else {
                                loadProgressEmbed.spliceFields(1, 1, { name: 'Deleted Roles', value: 'Skipped', inline: true });
                                await interaction.editReply({ embeds: [loadProgressEmbed] });
                            }

                            // --- Recreate roles ---
                            loadProgressEmbed.spliceFields(2, 1, { name: 'Created Roles', value: 'In Progress...', inline: true });
                            await i.editReply({ embeds: [loadProgressEmbed] });

                            const newRoleIds = {};
                            const totalRolesToCreate = backup.data.roles.length;
                            let createdRoleCount = 0;

                            for (const roleData of backup.data.roles.sort((a, b) => a.position - b.position)) {
                                try {
                                    const newRole = await interaction.guild.roles.create({
                                        name: roleData.name,
                                        color: roleData.color,
                                        hoist: roleData.hoist,
                                        mentionable: roleData.mentionable,
                                        permissions: getPermissionBigInt(roleData.permissions),
                                        position: roleData.position
                                    });
                                    newRoleIds[roleData.id] = newRole.id;
                                } catch (roleError) {
                                    console.error(`Failed to create role ${roleData.name}:`, roleError);
                                }
                                createdRoleCount++;
                                const progress = Math.floor((createdRoleCount / totalRolesToCreate) * 100);
                                loadProgressEmbed.spliceFields(2, 1, { name: 'Created Roles', value: `${progress}%`, inline: true });
                                await i.editReply({ embeds: [loadProgressEmbed] });
                            }
                            loadProgressEmbed.spliceFields(2, 1, { name: 'Created Roles', value: 'Complete!', inline: true });
                            await i.editReply({ embeds: [loadProgressEmbed] });

                            // --- Recreate channels ---
                            loadProgressEmbed.spliceFields(3, 1, { name: 'Created Channels', value: 'In Progress...', inline: true });
                            await i.editReply({ embeds: [loadProgressEmbed] });

                            const newChannelIds = {};
                            const categoriesToCreate = backup.data.channels.filter(c => c.type === ChannelType.GuildCategory).sort((a, b) => a.position - b.position);
                            const otherChannelsToCreate = backup.data.channels.filter(c => c.type !== ChannelType.GuildCategory).sort((a, b) => a.position - b.position);

                            const totalChannelsToCreate = categoriesToCreate.length + otherChannelsToCreate.length;
                            let createdChannelCount = 0;

                            // Create categories first
                            for (const channelData of categoriesToCreate) {
                                try {
                                    const newChannel = await interaction.guild.channels.create({
                                        name: channelData.name,
                                        type: channelData.type,
                                        topic: channelData.topic,
                                        nsfw: channelData.nsfw,
                                        position: channelData.position,
                                        permissionOverwrites: channelData.permissionOverwrites.map(overwrite => ({
                                            id: newRoleIds[overwrite.id] || overwrite.id,
                                            type: overwrite.type,
                                            allow: getPermissionBigInt(overwrite.allow),
                                            deny: getPermissionBigInt(overwrite.deny)
                                        }))
                                    });
                                    newChannelIds[channelData.id] = newChannel.id;
                                } catch (channelError) {
                                    console.error(`Failed to create category ${channelData.name}:`, channelError);
                                }
                                createdChannelCount++;
                                const progress = Math.floor((createdChannelCount / totalChannelsToCreate) * 100);
                                loadProgressEmbed.spliceFields(3, 1, { name: 'Created Channels', value: `${progress}%`, inline: true });
                                await i.editReply({ embeds: [loadProgressEmbed] });
                            }

                            // Create other channels
                            for (const channelData of otherChannelsToCreate) {
                                try {
                                    const newChannel = await interaction.guild.channels.create({
                                        name: channelData.name,
                                        type: channelData.type,
                                        topic: channelData.topic,
                                        nsfw: channelData.nsfw,
                                        parent: channelData.parentId ? newChannelIds[channelData.parentId] : null,
                                        position: channelData.position,
                                        permissionOverwrites: channelData.permissionOverwrites.map(overwrite => ({
                                            id: newRoleIds[overwrite.id] || overwrite.id,
                                            type: overwrite.type,
                                            allow: getPermissionBigInt(overwrite.allow),
                                            deny: getPermissionBigInt(overwrite.deny)
                                        }))
                                    });
                                    newChannelIds[channelData.id] = newChannel.id;
                                } catch (channelError) {
                                    console.error(`Failed to create channel ${channelData.name}:`, channelError);
                                }
                                createdChannelCount++;
                                const progress = Math.floor((createdChannelCount / totalChannelsToCreate) * 100);
                                loadProgressEmbed.spliceFields(3, 1, { name: 'Created Channels', value: `${progress}%`, inline: true });
                                await i.editReply({ embeds: [loadProgressEmbed] });
                            }
                            loadProgressEmbed.spliceFields(3, 1, { name: 'Created Channels', value: 'Complete!', inline: true });
                            await i.editReply({ embeds: [loadProgressEmbed] });

                            // --- Restore messages ---
                            if (backup.data.messages && Object.keys(backup.data.messages).length > 0) {
                                loadProgressEmbed.spliceFields(4, 1, { name: 'Restoring Messages', value: 'In Progress...', inline: true });
                                await i.editReply({ embeds: [loadProgressEmbed] });

                                let restoredMessageChannelCount = 0;
                                const totalMessageChannelsToRestore = Object.keys(backup.data.messages).length;

                                for (const channelId in backup.data.messages) {
                                    const newChannelId = newChannelIds[channelId];
                                    if (newChannelId) {
                                        const targetChannel = interaction.guild.channels.cache.get(newChannelId);
                                        if (targetChannel && targetChannel.type === ChannelType.GuildText) {
                                            for (const message of backup.data.messages[channelId]) {
                                                try {
                                                    let messageToSend = "";
                                                    let filesToSend = message.attachments.map(att => att.url);

                                                    // Fetch author's username
                                                    let authorName = "Unknown User";
                                                    try {
                                                        const author = await interaction.client.users.fetch(message.authorId);
                                                        authorName = author.username;
                                                    } catch (authorFetchError) {
                                                        console.error(`Failed to fetch author ${message.authorId}:`, authorFetchError);
                                                    }

                                                    if (message.content) {
                                                        messageToSend = `**${authorName}**: ${message.content}`;
                                                    } else if (filesToSend.length > 0) {
                                                        messageToSend = `**${authorName}**: (Attachment)`;
                                                    } else {
                                                        messageToSend = `**${authorName}**: could not load message`;
                                                    }

                                                   if (messageToSend || filesToSend.length > 0) {
                                                       if (messageToSend.length > 2000) {
                                                           const chunks = messageToSend.match(/[\s\S]{1,2000}/g) || [];
                                                           for (let i = 0; i < chunks.length; i++) {
                                                               if (i === 0) {
                                                                   // Send first chunk with any files
                                                                   await targetChannel.send({ content: chunks[i], files: filesToSend });
                                                               } else {
                                                                   // Send subsequent chunks
                                                                   await targetChannel.send({ content: chunks[i] });
                                                               }
                                                           }
                                                       } else {
                                                           // If message is not too long, send as is
                                                           await targetChannel.send({
                                                               content: messageToSend,
                                                               files: filesToSend
                                                           });
                                                       }
                                                   }
                                                } catch (messageError) {
                                                    console.error(`Failed to send message to channel ${targetChannel.name} (${targetChannel.id}):`, messageError);
                                                }
                                            }
                                        }
                                    }
                                    restoredMessageChannelCount++;
                                    const progress = Math.floor((restoredMessageChannelCount / totalMessageChannelsToRestore) * 100);
                                    loadProgressEmbed.spliceFields(4, 1, { name: 'Restoring Messages', value: `${progress}%`, inline: true });
                                    await i.editReply({ embeds: [loadProgressEmbed] });
                                }
                                loadProgressEmbed.spliceFields(4, 1, { name: 'Restoring Messages', value: 'Complete!', inline: true });
                                await i.editReply({ embeds: [loadProgressEmbed] });
                            } else {
                                loadProgressEmbed.spliceFields(4, 1, { name: 'Restoring Messages', value: 'Skipped (no messages in backup)', inline: true });
                                await i.editReply({ embeds: [loadProgressEmbed] });
                            }

                            loadProgressEmbed.setDescription(`Backup \`${backup.backupId}\` loaded successfully to **${interaction.guild.name}**.`);
                            loadProgressEmbed.setColor('Green');
                            await i.editReply({ embeds: [loadProgressEmbed] });

                        } catch (restoreError) {
                            console.error('Error during restoration:', restoreError);
                            loadProgressEmbed.setDescription(`Failed to load backup \`${backup.backupId}\`. An error occurred during restoration.`);
                            loadProgressEmbed.setColor('Red');
                            await i.editReply({ embeds: [loadProgressEmbed] });
                        }

                    } else if (i.customId === 'cancel_load_backup') {
                        await i.update({ embeds: [], components: [], content: 'Backup loading cancelled.' });
                    }
                });

                collector.on('end', collected => {
                    if (collected.size === 0) {
                        interaction.editReply({ embeds: [], components: [], content: 'Backup loading timed out.' });
                    }
                });

            } catch (error) {
                console.error('Error loading backup:', error);
                await interaction.editReply('Failed to load backup. Please try again later.');
            }
        } else if (subcommand === 'delete') {
            await interaction.deferReply({ flags: MessageFlags.Ephemeral });
            const backupId = interaction.options.getString('backup_id');

            try {
                const result = await Backup.deleteOne({ userId: interaction.user.id, backupId: backupId });

                if (result.deletedCount === 0) {
                    return await interaction.editReply(`Backup with ID \`${backupId}\` not found or does not belong to you.`);
                }

                await interaction.editReply(`Backup \`${backupId}\` deleted successfully.`);
            } catch (error) {
                console.error('Error deleting backup:', error);
                await interaction.editReply('Failed to delete backup. Please try again later.');
            }
        }
    },
};