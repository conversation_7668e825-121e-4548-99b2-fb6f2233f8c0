const Discord = require('discord.js');
const { REST } = require('discord.js');
const { Routes } = require('discord.js');
const chalk = require('chalk');
const fs = require('fs');
const path = require('path');

module.exports = (client) => {
    const interactionLogs = new Discord.WebhookClient({
        id: client.webhooks.interactionLogs.id,
        token: client.webhooks.interactionLogs.token,
    });

    const commands = [];
    client.commands = new Discord.Collection();

    if (client.shard.ids[0] === 0) console.log(chalk.blue(chalk.bold(`System`)), (chalk.white(`>>`)), (chalk.green(`Loading commands`)), (chalk.white(`...`)))
    if (client.shard.ids[0] === 0) console.log(`\u001b[0m`);

    // Load commands from src/commands (for index.js files)
    const commandFolders = fs.readdirSync('./src/commands');
    for (const folder of commandFolders) {
        const folderPath = `./src/commands/${folder}`;
        const indexFilePath = `${folderPath}/index.js`;

        if (fs.existsSync(indexFilePath)) {
            const command = require(`${process.cwd()}/${indexFilePath}`);
            if (command.data) {
                commands.push(command.data.toJSON());
                client.commands.set(command.data.name, command);
                if (client.shard.ids[0] === 0) console.log(chalk.blue(chalk.bold(`System`)), (chalk.white(`>>`)), chalk.red(`1`), (chalk.green(`command of`)), chalk.red(`${folder}`), (chalk.green(`loaded`)));
            }
        }
    }

    // Load commands from src/interactions/Command
    const interactionCommandFiles = fs.readdirSync('./src/interactions/Command').filter(file => file.endsWith('.js'));

    if (client.shard.ids[0] === 0) console.log(chalk.blue(chalk.bold(`System`)), (chalk.white(`>>`)), chalk.red(`${interactionCommandFiles.length}`), (chalk.green(`commands of`)), chalk.red(`Command`), (chalk.green(`loaded`)));

    for (const file of interactionCommandFiles) {
        const command = require(`${process.cwd()}/src/interactions/Command/${file}`);
        if (command.data) {
            commands.push(command.data.toJSON());
            client.commands.set(command.data.name, command);
        }
    }

    const rest = new REST({ version: '9' }).setToken(process.env.DISCORD_TOKEN);

    (async () => {
        try {
            const embed = new Discord.EmbedBuilder()
                .setDescription(`Started refreshing application (/) commands.`)
                .setColor(client.config.colors.normal)
            interactionLogs.send({
                username: 'Bot Logs',
                embeds: [embed]
            });

            await rest.put(
                Routes.applicationCommands(client.config.discord.id),
                { body: commands },
            )

            const embedFinal = new Discord.EmbedBuilder()
                .setDescription(`Successfully reloaded ${commands.length} application (/) commands.`)
                .setColor(client.config.colors.normal)
            interactionLogs.send({
                username: 'Bot Logs',
                embeds: [embedFinal]
            });

        } catch (error) {
            console.log(error);
        }
    })();
}