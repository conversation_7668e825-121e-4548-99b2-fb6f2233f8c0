# Dokploy Deployment Guide for Nexoria Bot + Dashboard

## 🚀 Quick Deployment Steps

### 1. **Update Your Existing Bot on Dokploy**

Since you already have your bot deployed, you just need to:

1. **Push the new code** to your repository (the API server is now integrated)
2. **Update environment variables** in Dokploy
3. **Redeploy** your existing service

### 2. **Environment Variables to Add in Dokploy**

Add these to your existing bot's environment variables:

```env
# API Server (required for dashboard)
API_PORT=4000
API_TOKEN=your-secure-random-token

# Dashboard OAuth (get from Discord Developer Portal)
NEXTAUTH_SECRET=another-random-string
DISCORD_AUTH_SECRET=your-discord-app-secret
```

### 3. **Port Configuration**

In Dokploy, make sure to expose both ports:
- **Port 4000**: Bot API (internal)
- **Port 3000**: Dashboard (public)

## 🔧 Two Deployment Options

### **Option A: Update Existing Deployment (Recommended)**

1. **Keep your current bot deployment**
2. **Add dashboard as a separate service**
3. **Use internal networking**

#### Steps:
1. In Dokploy, create a **new service** for the dashboard
2. Use the `dashboard/Dockerfile` 
3. Set environment variables:
   ```env
   YOUR_BOT_API_URL=http://your-bot-service:4000
   BASE_URL=https://your-dashboard-domain.com
   NEXTAUTH_URL=https://your-dashboard-domain.com
   ```

### **Option B: Combined Deployment**

1. **Replace your current deployment** with the combined version
2. **Use the `Dockerfile.combined`**
3. **Single service** running both bot and dashboard

## 🔑 Required Environment Variables

Copy from `dokploy.env.example` and update these values:

### **Essential Variables:**
```env
# Your existing bot variables
DISCORD_TOKEN=your_bot_token
MONGO_TOKEN=your_mongodb_string
DISCORD_ID=your_bot_id

# New API variables
API_TOKEN=generate-random-uuid
NEXTAUTH_SECRET=generate-random-string
DISCORD_AUTH_SECRET=from-discord-developer-portal
```

### **Generate Secure Tokens:**
```bash
# For API_TOKEN and NEXTAUTH_SECRET
node -e "console.log(require('crypto').randomUUID())"
```

## 🌐 Discord Developer Portal Setup

1. Go to [Discord Developer Portal](https://discord.com/developers/applications)
2. Select your bot application
3. Go to **OAuth2** → **General**
4. Add redirect URL: `https://your-dashboard-domain.com/api/auth/callback/discord`
5. Copy the **Client Secret** for `DISCORD_AUTH_SECRET`

## 📋 Deployment Checklist

- [ ] Code pushed to repository
- [ ] Environment variables configured in Dokploy
- [ ] Ports 3000 and 4000 exposed
- [ ] Discord OAuth redirect URL added
- [ ] Domain configured for dashboard
- [ ] SSL certificate enabled

## 🔍 Testing the Deployment

After deployment:

1. **Check bot API**: `https://your-domain.com:4000/health`
2. **Access dashboard**: `https://your-dashboard-domain.com`
3. **Test login**: Click "Sign in with Discord"
4. **Verify data**: Check if bot stats appear

## 🐛 Troubleshooting

### **Bot API not responding:**
- Check if port 4000 is exposed in Dokploy
- Verify `API_TOKEN` matches in both services
- Check container logs for API server startup

### **Dashboard login fails:**
- Verify `DISCORD_AUTH_SECRET` is correct
- Check redirect URL in Discord Developer Portal
- Ensure `NEXTAUTH_URL` matches your domain

### **"Bot not found" error:**
- Verify `YOUR_BOT_ID` matches your Discord bot ID
- Check `YOUR_BOT_API_URL` points to correct service
- Ensure both services can communicate

## 🎯 Production Optimizations

1. **Use internal networking** between bot and dashboard
2. **Set up proper logging** for monitoring
3. **Configure health checks** in Dokploy
4. **Enable SSL** for secure connections
5. **Set up monitoring** for both services

## 📊 What You'll Get

Once deployed, your dashboard will provide:

- ✅ **Real-time bot monitoring**
- ✅ **Server/guild management**
- ✅ **Command usage statistics**
- ✅ **Performance metrics**
- ✅ **Maintenance mode toggle**
- ✅ **Beautiful charts and graphs**

## 🔄 Next Steps After Deployment

1. **Test all dashboard features**
2. **Set up monitoring alerts**
3. **Configure backup strategies**
4. **Customize dashboard branding**
5. **Add additional bot owners if needed**

Your existing bot will continue working normally, but now with a powerful web dashboard for management!
