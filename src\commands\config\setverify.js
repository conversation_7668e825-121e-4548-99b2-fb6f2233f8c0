const Discord = require('discord.js');
const { SlashCommandBuilder } = require('@discordjs/builders');

const Schema = require("../../database/models/verify");

module.exports = {
    data: new SlashCommandBuilder()
        .setName('setverify')
        .setDescription('Set up the verification system.')
        .addBooleanOption(option =>
            option.setName('enable')
                .setDescription('Enable or disable the verification system.')
                .setRequired(true))
        .addChannelOption(option =>
            option.setName('channel')
                .setDescription('The channel where the verification message will be sent.')
                .setRequired(true))
        .addRoleOption(option =>
            option.setName('role')
                .setDescription('The role to give upon verification.')
                .setRequired(true)),
    async execute(client, interaction, args) {
        const perms = await client.checkUserPerms({
            flags: [Discord.PermissionsBitField.Flags.ManageMessages],
            perms: [Discord.PermissionsBitField.Flags.ManageMessages]
        }, interaction)

        if (perms == false) return;

        const boolean = interaction.options.getBoolean('enable');
        const channel = interaction.options.getChannel('channel');
        const role = interaction.options.getRole('role');

        if (boolean == true) {
            const data = await Schema.findOne({ Guild: interaction.guild.id });
            if (data) {
                data.Channel = channel.id;
                data.Role = role.id
                data.save();
            }
            else {
                new Schema({
                    Guild: interaction.guild.id,
                    Channel: channel.id,
                    Role: role.id
                }).save();
            }

            client.succNormal({
                text: `Verify panel has been successfully created`,
                fields: [
                    {
                        name: `📘┆Channel`,
                        value: `${channel} (${channel.name})`,
                        inline: true
                    },
                    {
                        name: `📛┆Role`,
                        value: `${role} (${role.name})`,
                        inline: true
                    }
                ],
                type: 'editreply'
            }, interaction);

            const row = new Discord.ActionRowBuilder()
                .addComponents(
                    new Discord.ButtonBuilder()
                        .setCustomId('Bot_verify')
                        .setEmoji('✅')
                        .setStyle(Discord.ButtonStyle.Success),
                );

            client.embed({
                title: `${interaction.guild.name}・verify`,
                desc: `Click on the button to verify yourself`,
                components: [row]
            }, channel)
        }
    },
};

 