const Discord = require('discord.js');
const { SlashCommandBuilder } = require('@discordjs/builders');

const logs = require("../../database/models/logChannels");
const boostLogs = require("../../database/models/boostChannels");
const levelLogs = require("../../database/models/levelChannels");

module.exports = {
    data: new SlashCommandBuilder()
        .setName('logs')
        .setDescription('Setup log channels.')
        .addStringOption(option =>
            option.setName('setup')
                .setDescription('The log channel to set up.')
                .setRequired(true)
                .addChoices(
                    { name: 'Server Logs', value: 'serverLogs' },
                    { name: 'Level Logs', value: 'levelLogs' },
                    { name: 'Boost Logs', value: 'boostLogs' }
                )),
    async execute(client, interaction, args) {
        const choice = interaction.options.getString('setup');

        if (choice == "serverLogs") {
            interaction.guild.channels.create({
                name: "server-logs",
                permissionOverwrites: [
                    {
                        deny: [Discord.PermissionsBitField.Flags.ViewChannel],
                        id: interaction.guild.id
                    },
                ],
                type: Discord.ChannelType.GuildText
            }).then((ch) => {
                client.createChannelSetup(logs, ch, interaction)
            })
        }

        if (choice == "levelLogs") {
            interaction.guild.channels.create({
                name: "level-logs",
                type: Discord.ChannelType.GuildText
            }).then((ch) => {
                client.createChannelSetup(levelLogs, ch, interaction)
            })
        }

        if (choice == "boostLogs") {
            interaction.guild.channels.create({
                name: "boosts",
                type: Discord.ChannelType.GuildText
            }).then((ch) => {
                client.createChannelSetup(boostLogs, ch, interaction)
            })
        }
    },
};

 

