const Discord = require('discord.js');
const { SlashCommandBuilder } = require('@discordjs/builders');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('edit')
        .setDescription('Edit an existing announcement.')
        .addStringOption(option =>
            option.setName('message')
                .setDescription('The new message for the announcement.')
                .setRequired(true))
        .addStringOption(option =>
            option.setName('id')
                .setDescription('The ID of the announcement message to edit.')
                .setRequired(true)),
    async execute(client, interaction, args) {
        const message = interaction.options.getString('message');
        const messageId = interaction.options.getString('id');

        const editMessage = await interaction.channel.messages.fetch(messageId);

        client.embed({ 
            title: `📢・Announcement!`, 
            desc: message,
            type: 'edit'
        }, editMessage);

        client.succNormal({
            text: `Announcement has been edit successfully!`,
            type: 'ephemeraledit'
        }, interaction);
    },
};

 