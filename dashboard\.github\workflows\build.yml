name: Build

on:
  pull_request:
  # allows you to run this workflow manually from the Actions tab
  workflow_dispatch:
  # allow the workflow to be re-used in other workflows
  workflow_call:

jobs:

  build:

    name: Build
    runs-on: ubuntu-latest
    steps:

      - uses: actions/checkout@v3

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: 20.10.0
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Build
        run: npm run build