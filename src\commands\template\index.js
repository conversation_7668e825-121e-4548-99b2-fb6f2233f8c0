const { <PERSON><PERSON><PERSON><PERSON><PERSON>Builder, PermissionsBitField, OverwriteType } = require('discord.js');
const Template = require('../../database/models/template');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('template')
        .setDescription('Manage server templates.')
        .addSubcommand(subcommand =>
            subcommand
                .setName('load')
                .setDescription('Loads a public or Nexoria server template.')
                .addStringOption(option =>
                    option.setName('name_or_id')
                        .setDescription('The name, ID, or direct link of the template.')
                        .setRequired(true)
                )
                .addStringOption(option =>
                    option.setName('options')
                        .setDescription('Options to disable loading of channels, roles, settings individually (e.g., !channels, !roles).')
                        .setRequired(false)
                )
        )
        .addSubcommand(subcommand =>
            subcommand
                .setName('add')
                .setDescription('[OWNER ONLY] Adds the current server as a template.')
                .addStringOption(option =>
                    option.setName('name')
                        .setDescription('A unique name for the template.')
                        .setRequired(true)
                )
                .addStringOption(option =>
                    option.setName('description')
                        .setDescription('A brief description of the template.')
                        .setRequired(false)
                )
        )
        .addSubcommand(subcommand =>
            subcommand
                .setName('list')
                .setDescription('Lists all available templates.')
        )
        .addSubcommand(subcommand =>
            subcommand
                .setName('delete')
                .setDescription('[OWNER ONLY] Deletes a template by its ID.')
                .addStringOption(option =>
                    option.setName('template_id')
                        .setDescription('The ID of the template to delete.')
                        .setRequired(true)
                )
        ),
    async execute(interaction, client) {
        const subcommand = interaction.options.getSubcommand();
        const owners = process.env.OWNERS ? process.env.OWNERS.split(',') : [];
        const isOwner = owners.includes(interaction.user.id);

        if (subcommand === 'load') {
            await interaction.deferReply({ ephemeral: true });

            const perms = await client.checkPerms({
                flags: [PermissionsBitField.Flags.Administrator],
                perms: [PermissionsBitField.Flags.Administrator],
            }, interaction);
            if (perms === false) return;

            const nameOrId = interaction.options.getString('name_or_id');
            const options = interaction.options.getString('options');

            try {
                // Try to find by templateId first
                let template = await Template.findOne({ templateId: nameOrId });

                // If not found by ID, try to find by name
                if (!template) {
                    template = await Template.findOne({ name: nameOrId });
                }

                if (!template) {
                    return await interaction.editReply(`Template with name or ID \`${nameOrId}\` not found.`);
                }

                let replyMessage = `Found template **${template.name}** (ID: \`${template.templateId}\`).\nStarting to load the template...`;
                await interaction.editReply(replyMessage);

                // --- Placeholder for actual template loading logic ---
                // Parse options to determine what to load/skip
                const skipChannels = options && options.includes('!channels');
                const skipRoles = options && options.includes('!roles');
                const skipSettings = options && options.includes('!settings');

                if (!skipSettings) {
                    // Logic to apply guild settings (e.g., name, icon, verification level)
                    // console.log('Applying guild settings...');
                }

                if (!skipRoles) {
                    // Logic to create/update roles
                    // console.log('Creating/updating roles...');
                }

                if (!skipChannels) {
                    // Logic to create/update channels and categories
                    // console.log('Creating/updating channels...');
                }

                replyMessage += `\nTemplate loading process initiated.`;
                if (skipChannels) replyMessage += `\n- Skipping channel creation.`;
                if (skipRoles) replyMessage += `\n- Skipping role creation.`;
                if (skipSettings) replyMessage += `\n- Skipping guild settings application.`;
                replyMessage += `\n(Actual server modification logic is not yet implemented.)`;

                await interaction.editReply(replyMessage);

            } catch (error) {
                console.error('Error loading template:', error);
                await interaction.editReply('Failed to load template. Please try again later.');
            }
        } else if (subcommand === 'add') {
            await interaction.deferReply({ ephemeral: true });
            // Owner check
            if (!isOwner) {
                return await interaction.editReply('This command can only be used by the bot owner.');
            }

            const templateName = interaction.options.getString('name');
            const templateDescription = interaction.options.getString('description') || 'No description provided.';

            try {
                // Check if a template with this name already exists
                const existingTemplate = await Template.findOne({ name: templateName });
                if (existingTemplate) {
                    return await interaction.editReply(`A template with the name \`${templateName}\` already exists. Please choose a different name.`);
                }

                await interaction.editReply('Extracting server data... This may take a moment.');

                const guild = interaction.guild;

                // Extract Roles
                const roles = guild.roles.cache
                    .filter(role => !role.managed && role.name !== '@everyone')
                    .sort((a, b) => b.position - a.position) // Sort to create them in the correct order later
                    .map(role => ({
                        name: role.name,
                        color: role.hexColor,
                        hoist: role.hoist,
                        permissions: role.permissions.bitfield.toString(),
                        mentionable: role.mentionable,
                    }));

                // Extract Channels and Categories
                const channels = guild.channels.cache
                    .sort((a, b) => a.position - b.position)
                    .map(channel => {
                        const channelData = {
                            type: channel.type,
                            name: channel.name,
                            topic: channel.topic,
                            nsfw: channel.nsfw,
                            position: channel.position,
                            parent: channel.parent ? channel.parent.name : null,
                            permissionOverwrites: channel.permissionOverwrites.cache.map(overwrite => ({
                                id: overwrite.type === OverwriteType.Role ? guild.roles.cache.get(overwrite.id)?.name : overwrite.id,
                                type: overwrite.type === OverwriteType.Role ? 'role' : 'member',
                                allow: overwrite.allow.bitfield.toString(),
                                deny: overwrite.deny.bitfield.toString(),
                            })),
                        };
                        if (channel.isVoiceBased()) {
                            channelData.bitrate = channel.bitrate;
                            channelData.userLimit = channel.userLimit;
                        }
                        return channelData;
                    });

                // Construct the template data
                const templateData = {
                    guildName: guild.name,
                    iconURL: guild.iconURL({ dynamic: true, size: 4096 }),
                    roles: roles,
                    channels: channels,
                };

                const newTemplate = new Template({
                    templateId: `${interaction.guild.id}-${Date.now()}`,
                    name: templateName,
                    description: templateDescription,
                    data: templateData,
                    creatorId: interaction.user.id
                });

                await newTemplate.save();
                await interaction.editReply(`Server structure extracted. Template **${templateName}** created successfully with ID: \`${newTemplate.templateId}\`.`);
            } catch (error) {
                console.error('Error adding template:', error);
                await interaction.editReply('Failed to add template. Please try again later.');
            }
        } else if (subcommand === 'list') {
            await interaction.deferReply({ ephemeral: true });

            try {
                const templates = await Template.find({}); // Fetch all templates

                if (templates.length === 0) {
                    return await interaction.editReply('No templates found.');
                }

                let replyMessage = '**Available Templates:**\n';
                templates.forEach((template, index) => {
                    replyMessage += `\n${index + 1}. Name: **${template.name}** | ID: \`${template.templateId}\` (Created by: <@${template.creatorId}>)`;
                });

                await interaction.editReply(replyMessage);
            } catch (error) {
                console.error('Error listing templates:', error);
                await interaction.editReply('Failed to retrieve templates. Please try again later.');
            }
        } else if (subcommand === 'delete') {
            await interaction.deferReply({ ephemeral: true });
            // Owner check
            if (!isOwner) {
                return await interaction.editReply('This command can only be used by the bot owner.');
            }

            const templateId = interaction.options.getString('template_id');

            try {
                const result = await Template.deleteOne({ templateId: templateId });

                if (result.deletedCount === 0) {
                    return await interaction.editReply(`Template with ID \`${templateId}\` not found.`);
                }

                await interaction.editReply(`Template \`${templateId}\` deleted successfully.`);
            } catch (error) {
                console.error('Error deleting template:', error);
                await interaction.editReply('Failed to delete template. Please try again later.');
            }
        }
    },
};