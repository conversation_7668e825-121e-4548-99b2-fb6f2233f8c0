const Discord = require('discord.js');
const { SlashCommandBuilder } = require('@discordjs/builders');

const Birthdays = require("../../database/models/birthdaychannels");
const Chatbot = require("../../database/models/chatbot-channel");
const Review = require("../../database/models/reviewChannels");
const Suggestion = require("../../database/models/suggestionChannels");
const StarBoard = require("../../database/models/starboardChannels");

module.exports = {
    data: new SlashCommandBuilder()
        .setName('fun')
        .setDescription('Setup fun channels.')
        .addStringOption(option =>
            option.setName('setup')
                .setDescription('The fun channel to set up.')
                .setRequired(true)
                .addChoices(
                    { name: 'Birthdays', value: 'birthdays' },
                    { name: 'Chatbot', value: 'chatbot' },
                    { name: 'Reviews', value: 'reviews' },
                    { name: 'Suggestions', value: 'suggestions' },
                    { name: 'Starboard', value: 'starboard' }
                )),
    async execute(client, interaction, args) {
        const choice = interaction.options.getString('setup');

        if (choice == "birthdays") {
            interaction.guild.channels.create({
                name: "birthdays",
                type: Discord.ChannelType.GuildText
            }).then((ch) => {
                client.createChannelSetup(Birthdays, ch, interaction)
            })
        }

        if (choice == "chatbot") {
            interaction.guild.channels.create({
                name: "chatbot",
                type: Discord.ChannelType.GuildText
            }).then((ch) => {
                client.createChannelSetup(Chatbot, ch, interaction)
            })
        }

        if (choice == "reviews") {
            interaction.guild.channels.create({
                name: "reviews",
                type: Discord.ChannelType.GuildText
            }).then((ch) => {
                client.createChannelSetup(Review, ch, interaction)
            })
        }

        if (choice == "suggestions") {
            interaction.guild.channels.create({
                name: "suggestions",
                type: Discord.ChannelType.GuildText
            }).then((ch) => {
                client.createChannelSetup(Suggestion, ch, interaction)
            })
        }

        if (choice == "starboard") {
            interaction.guild.channels.create({
                name: "starboard",
                type: Discord.ChannelType.GuildText
            }).then((ch) => {
                client.createChannelSetup(StarBoard, ch, interaction)
            })
        }
    },
};

