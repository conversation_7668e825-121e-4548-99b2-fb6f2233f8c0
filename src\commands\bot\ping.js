const Discord = require('discord.js');
const { SlashCommandBuilder } = require('@discordjs/builders');
const mongoose = require('mongoose');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('ping')
        .setDescription('Shows the bot\'s ping.'),
    async execute(client, interaction, args) {
        client.simpleEmbed({
            desc: `${client.emotes.animated.loading} Calculating ping...`,
            type: 'editreply'
        }, interaction).then((resultMessage) => {
            const ping = Math.floor(resultMessage.createdTimestamp - interaction.createdTimestamp);

            mongoose.connection.db.admin().ping(function (err, result) {

                var mongooseSeconds = ((result.ok % 60000) / 1000);
                var pingSeconds = ((ping % 60000) / 1000);
                var apiSeconds = ((client.ws.ping % 60000) / 1000);

                client.embed({
                    title: `${client.emotes.normal.pong}・Pong`,
                    desc: `Check out how fast our bot is`,
                    fields: [
                        {
                            name: "🤖┆Bot latency",
                            value: `${ping}ms (${pingSeconds}s)`,
                            inline: true,
                        },
                        {
                            name: "💻┆API Latency",
                            value: `${client.ws.ping}ms (${apiSeconds}s)`,
                            inline: true,
                        },
                        {
                            name: "📂┆Database Latency",
                            value: `${result.ok}ms (${mongooseSeconds}s)`,
                            inline: true,
                        }
                    ],
                    type: 'editreply'
                }, interaction)
            })
        })
    },
};

 