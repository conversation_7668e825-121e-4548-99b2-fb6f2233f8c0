const Discord = require('discord.js');
const { SlashCommandBuilder } = require('@discordjs/builders');

const Schema = require("../../database/models/blacklist");

module.exports = {
    data: new SlashCommandBuilder()
        .setName('remove')
        .setDescription('Remove a word from the blacklist.')
        .addStringOption(option =>
            option.setName('word')
                .setDescription('The word to remove from the blacklist.')
                .setRequired(true)),
    async execute(client, interaction, args) {
        const word = interaction.options.getString('word');

        Schema.findOne({ Guild: interaction.guild.id }, async (err, data) => {
            if (data) {
                if (!data.Words.includes(word)) {
                    return client.errNormal({
                        error: `That word doesn't exist in the database!`,
                        type: 'editreply'
                    }, interaction);
                }

                const filtered = data.Words.filter((target) => target !== word);

                await Schema.findOneAndUpdate({ Guild: interaction.guild.id }, {
                    Guild: interaction.guild.id,
                    Words: filtered
                });

                client.succNormal({
                    text: `Word is removed from the blacklist!`,
                    fields: [
                        {
                            name: `💬┆Word`,
                            value: `${word}`
                        }
                    ],
                    type: 'editreply'
                }, interaction);
            }
            else {
                client.errNormal({
                    error: `This guild has not data!`,
                    type: 'editreply'
                }, interaction);
            }
        })
    },
};

 