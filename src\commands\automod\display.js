const Discord = require('discord.js');
const { SlashCommandBuilder } = require('@discordjs/builders');

const Schema = require("../../database/models/blacklist");

module.exports = {
    data: new SlashCommandBuilder()
        .setName('display')
        .setDescription('Display blacklisted words.'),
    async execute(client, interaction, args) {
        Schema.findOne({ Guild: interaction.guild.id }, async (err, data) => {
            if (data && data.Words.length > 0) {
                client.embed({
                    title: "🤬・Blacklisted words",
                    desc: data.Words.join(", "),
                    type: 'editreply'
                }, interaction)
            }
            else {
                client.errNormal({
                    error: `This guild has not data!`,
                    type: 'editreply'
                }, interaction);
            }
        })
    },
};

 