const Discord = require('discord.js');
const { SlashCommandBuilder } = require('@discordjs/builders');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('changelogs')
        .setDescription('Shows the changelogs of the bot.'),
    async execute(client, interaction, args) {
        client.embed({
            title: "📃・Changelogs",
            desc: `_____`,
            thumbnail: client.user.avatarURL({ size: 1024 }),
            fields: [{
                name: "📃┆Changelogs",
                    value: '15/3/2023 Updated dependencies',
                    inline: false,
                },
            ],
            type: 'editreply'
        }, interaction)
    },
};

 
