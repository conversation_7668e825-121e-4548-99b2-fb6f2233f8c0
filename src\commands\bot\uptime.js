const Discord = require('discord.js');
const { SlashCommandBuilder } = require('@discordjs/builders');
const moment = require("moment");
require("moment-duration-format");

module.exports = {
    data: new SlashCommandBuilder()
        .setName('uptime')
        .setDescription("Shows the bot's uptime."),
    async execute(client, interaction, args) {
        const duration = moment.duration(client.uptime).format("`D` [days], `H` [hrs], `m` [mins], `s` [secs]");
        const upvalue = (Date.now() / 1000 - client.uptime / 1000).toFixed(0);

        client.embed({
            title: `${client.emotes.normal.arrowUp}・Uptime`,
            desc: `See the uptime of Bot`,
            fields: [
                {
                    name: "⌛┇Uptime",
                    value: `${duration}`,
                    inline: true
                },
                {
                    name: "⏰┇Up Since",
                    value: `<t:${upvalue}>`,
                    inline: true
                }
            ],
            type: 'editreply'
        }, interaction)
    },
};

 