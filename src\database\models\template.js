const mongoose = require('mongoose');

const templateSchema = new mongoose.Schema({
    templateId: { type: String, required: true, unique: true },
    name: { type: String, required: true },
    description: { type: String },
    data: { type: Object, required: true }, // Store all template data here
    creatorId: { type: String, required: true },
    createdAt: { type: Date, default: Date.now }
});

module.exports = mongoose.model('Template', templateSchema);