Change all Developer Commands to owner
Rebrand everything to Nexoria Development™


Addin these commands:
# Xenon Bot Command Reference

Xenon operates using **slash commands** (`/`), not prefix-based commands.

---

## 🛠️ Backup Commands

### `/backup create [message_count]`
- Creates a backup of the server (channels, roles, settings).
- If Premium, can include up to 250 messages per channel.
- Default message count depends on your plan.
:contentReference[oaicite:1]{index=1}

### `/backup list`
- Shows all backups you’ve created, including scheduled (“interval”) backups.
:contentReference[oaicite:2]{index=2}

### `/backup load <backup_id> [message_count] [options]`
- Restores a backup by its ID.
- Premium users may specify messages per channel.
- Default options: deletes existing and loads all settings, roles, channels.
- Options prefixed with `!` disable parts, e.g. `!delete-channels`.
:contentReference[oaicite:3]{index=3}

### `/backup delete <backup_id>`
- Permanently deletes a backup by ID.
:contentReference[oaicite:4]{index=4}

### `/backup interval on <interval> [message_count]`
- Enables automated backups at regular intervals (e.g. `24h`, `4h` for Premium).
- Only one “interval” backup per server, previous one is replaced.
:contentReference[oaicite:5]{index=5}

### `/backup interval show`
- Displays your current backup schedule and interval.
:contentReference[oaicite:6]{index=6}

---

## 📦 Template Commands

### `/template load <name_or_id> [options]`
- Loads a public or Xenon-hosted server template.
- Accepts a template **name**, **ID**, or direct **link**.
- Options can disable loading of channels, roles, settings individually.
:contentReference[oaicite:7]{index=7}

---

## 💬 Chatlogs

### `/chatlog create [channel|all]`
- Generates and saves a chat log of a given channel (or all channels).
- Returns a chatlog ID.
:contentReference[oaicite:8]{index=8}

### `/chatlog list`
- Lists your saved chatlogs with their IDs.
:contentReference[oaicite:9]{index=9}

---

## 🔄 Sync (Premium Feature)

- **Message syncing** between channels in different servers.
- **Ban syncing** across multiple servers.
- Can be set as uni- or bi-directional.
:contentReference[oaicite:10]{index=10}

---

## 📋 Summary Table

| Command| Description | Notes|
|----------------------------------------|-----------------------------------------------|------------------------------|
| `/backup create [message_count]` | Make a full server backup | Premium includes messages|
| `/backup list` | Display backups and schedules | Includes interval backups|
| `/backup load <id> ...`| Restore a backup| Can control what to load |
| `/backup delete <id>`| Delete a backup | Irreversible|
| `/backup interval on <interval> ...` | Enable scheduled backups| Interval depends on plan |
| `/backup interval show`| Display backup schedule ||
| `/template load <name_or_id> [opts]` | Load a template | Public or Xenon templates |
| `/chatlog create [channel]`| Save current chat logs| Gets chatlog ID |
| `/chatlog list`| List saved chat logs||
| Sync (messages, bans across servers) | Keep changes mirrored across servers| Premium only|

---

## ✅ How to Adapt These in Your Bot

To add similar functionality to your **own Discord bot**:

1. Implement slash commands matching each `/backup`, `/template`, and `/chatlog` subcommand.
2. Use Discord’s server API to read and write server settings (channels, roles, permissions, messages, bans).
3. For templates, support both public template links and internal template IDs, with fine-grained loading options.
4. For syncing, listen for events (like message create/delete or ban/unban) and propagate as needed.
5. Persist metadata like backup IDs, schedules, and chatlog IDs in a database.

---

### 📌 Final Notes

- Xenon **no longer uses legacy prefixes** like `x!`, apart from possible legacy premium interactions. Slash commands are the standard.
:contentReference[oaicite:11]{index=11}
- For the **latest list of subcommands or flags**, check the Xenon Wiki or support server.
- If you want example code snippets for **each command handler**, I’d be happy to help!

Let me know if you’d like setup templates or code scaffolding for any of these commands.
::contentReference[oaicite:12]{index=12}


Add in Premium for these:

Free:
Backup channels, roles & server settings 
Up to 20 backups 
1 Monthly auto backups 
Backup/Sync Bans

Premium 1: 
Backup channels, roles & server settings 
Up to 60 backups 
2 Monthly auto Backups 
Backup roles assignments, nicknames & bans 
Synchronize messages, role assignments & bans

Premium 2: 
Backup channels, roles & server settings 
Up to 150 backups 
4 Monthly auto Backups 
Backup roles assignments, nicknames & bans 
Backup messages (Up to 100 Per channel) 
Synchronize messages, role assignments & bans 
