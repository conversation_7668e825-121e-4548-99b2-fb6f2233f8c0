const Discord = require('discord.js');
const { SlashCommandBuilder } = require('@discordjs/builders');

const welcomeChannel = require("../../database/models/welcomeChannels");
const welcomeRole = require("../../database/models/joinRole");
const leaveChannel = require("../../database/models/leaveChannels");

module.exports = {
    data: new SlashCommandBuilder()
        .setName('welcome')
        .setDescription('Setup welcome and leave channels/roles.')
        .addStringOption(option =>
            option.setName('setup')
                .setDescription('The welcome/leave setup to configure.')
                .setRequired(true)
                .addChoices(
                    { name: 'Welcome Channel', value: 'welcomechannel' },
                    { name: 'Welcome Role', value: 'welcomerole' },
                    { name: 'Leave Channel', value: 'leavechannel' }
                )),
    async execute(client, interaction, args) {
        const choice = interaction.options.getString('setup');

        if (choice == "welcomechannel") {
            interaction.guild.channels.create({
                name: "Welcome",
                type: Discord.ChannelType.GuildText
            }).then((ch) => {
                client.createChannelSetup(welcomeChannel, ch, interaction)
            })
        }

        if (choice == "welcomerole") {
            interaction.guild.roles.create({
                name: 'Member',
                color: client.config.colors.normal
            }).then((rl) => {
                client.createRoleSetup(welcomeRole, rl, interaction)
            })
        }

        if (choice == "leavechannel") {
            interaction.guild.channels.create({
                name: "Bye",
                type: Discord.ChannelType.GuildText
            }).then((ch) => {
                client.createChannelSetup(leaveChannel, ch, interaction)
            })
        }
    },
};

 