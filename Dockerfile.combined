# Combined Dockerfile for Bot + Dashboard deployment on Dokploy
FROM node:20-alpine

WORKDIR /app

# Install dependencies for both bot and dashboard
COPY package*.json ./
COPY dashboard/package*.json ./dashboard/

# Install bot dependencies
RUN npm ci --only=production

# Install dashboard dependencies
WORKDIR /app/dashboard
RUN npm ci --only=production

# Build dashboard
COPY dashboard/ ./
RUN npm run build

# Copy bot source code
WORKDIR /app
COPY src/ ./src/
COPY .env.example ./

# Create startup script
RUN echo '#!/bin/sh' > start.sh && \
    echo 'echo "Starting Nexoria Bot with Dashboard..."' >> start.sh && \
    echo 'cd /app/dashboard && npm start &' >> start.sh && \
    echo 'cd /app && npm start' >> start.sh && \
    chmod +x start.sh

# Expose ports
EXPOSE 3000 4000

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:4000/health || exit 1

CMD ["./start.sh"]
