[![Version][version-shield]](version-url)
[![Contributors][contributors-shield]][contributors-url]
[![Forks][forks-shield]][forks-url]
[![Stargazers][stars-shield]][stars-url]
[![Issues][issues-shield]][issues-url]
[![MIT License][license-shield]][license-url]
<center><img src="https://capsule-render.vercel.app/api?type=waving&color=gradient&height=200&section=header&text=Discord-Bot&fontSize=80&fontAlignY=35&animation=twinkling&fontColor=gradient" /></center>

<br />

[![Run on Repl.it](https://repl.it/badge/github/Nexoria-Development/Discord-Bot)](https://replit.com/@CorwinDeveloper/Discord-Bot-v14?v=1)
[![Remix on Glitch](https://cdn.glitch.com/2703baf2-b643-4da7-ab91-7ee2a2d00b5b%2Fremix-button.svg)](https://glitch.com/edit/#!/import/github/Nexoria-Development/Discord-Bot)

<!-- PROJECT LOGO -->
<br />
<p align="center">
  <a href="https://github.com/Nexoria-Development/Discord-Bot">
    <img src="https://cdn.discordapp.com/attachments/778665159316209748/1055857473749274694/Corwin-1-modified.png" alt="Pbot-plus" width="200" height="200">
  </a>

  <h3 align="center">Discord-Bot</h3>

  <p align="center">
    Discord-bot is an updated version of <a href="https://github.com/DotwoodMedia/Dbot">Dbot</a>, an advanced Discord multipurpose bot containing more than 400 commands.<br> It can do Moderation, Tickets, Radio, Games, Giveaways, Customisation, Economy, Leveling, Invites, Messages, Utilities, Suggestions, Server Stats etc.<br> Unfortunately the owners stopped at the peak and decided to put the source online of which I made an updated version.
    <br />
    <br />
    <a href="https://github.com/Nexoria-Development/discord-bot/issues">Report Bug</a>
    ·
    <a href="https://github.com/Nexoria-Development/discord-bot/issues">Request Feature</a>
  </p>
</p>

<!-- NOTICE -->

### <img src="https://cdn.discordapp.com/emojis/1055803759831294013.png" width="20px" height="20px"> 》Notice 
> You may not claim this as your own! The original source was created by [Dotwood Media](https://github.com/DotwoodMedia) and [Graphix Development](https://github.com/GraphixDevelopment). The source is modified and updated by me(Nexoria Development™)

> Discord-Bot is a multipurpose Discord bot base in [Discord.js](https://github.com/Discordjs/discordjs)
If you like this repository, feel free to leave a star ⭐ to motivate me!

<!-- ABOUT THE PROJECT -->

## <img src="https://cdn.discordapp.com/emojis/859424401186095114.png" width="20px" height="20px">》Description 
[![Readme Card](https://github-readme-stats.vercel.app/api/pin/?username=Nexoria-Development&repo=Discord-bot&theme=tokyonight)](https://github.com/Nexoria-Development/Discord-bot)
## <img src="https://cdn.discordapp.com/emojis/852881450667081728.gif" width="20px" height="20px">》Feature
- [x] Slash Commands 
- [x] Upto date with Discord.js v14
- [x] Automod
- [x] Custom Commands
- [x] Music Commands
- [x] Tickets
- [x] Utility Commands
- [x] Suggestions 
- [x] Reaction Roles
- [x] Family
- [x] Giveaways 
- [x] Easy to use
- [x] Customizable
- [x] And much more
- [x] Don't wanna host it yourself? [Use our public bot](https://discord.com/api/oauth2/authorize?client_id=860390761307439114&permissions=8&scope=bot%20applications.commands)
## <img src="https://cdn.discordapp.com/emojis/1028680849195020308.png" width="25px" height="25px">》Screenshots
<br />
<p align="center">
  <a href="https://github.com/Nexoria-Development/discord-bot">
    <img src="https://cdn.discordapp.com/attachments/778665159316209748/1055832339328024666/207117434-d98356b1-bf19-418e-9e12-0ef83e0d9a21.png">
  </a>
</p>

## <img src="https://cdn.discordapp.com/emojis/1009754836314628146.gif" width="25px" height="25px">》Requirements
- NodeJs v17+
- Java v13 for lavalink server.
- Discord Token. Get it from [Discord Developers Portal](https://discord.com/developers/applications)
- Mongo Database URL. Get it from [MongoDB](https://cloud.mongodb.com/v2/635277bf9f5c7b5620db28a4#clusters)
- Giphy API Token. Get it from [Giphy Developers Portal](https://developers.giphy.com/)
- OpenAI API Key `for ai chatbot`. Get it from [OpenAi Developers Portal](https://beta.openai.com/account/api-keys)
- ClientID `for loading slash commands.` [Discord Developers Portal](https://discord.com/developers/applications)
- Spotify client ID `for Spotify support` [Click here to get](https://developer.spotify.com/dashboard/login)
- Spotify client Secret `for Spotify support` [Click here to get](https://developer.spotify.com/dashboard/login)

## <img src="https://cdn.discordapp.com/emojis/814216203466965052.png" width="25px" height="25px">》Installation Guide

### <img src="https://cdn.discordapp.com/emojis/1028680849195020308.png" width="15px" height="15px"> Installing via [NPM](https://www.npmjs.com/)
Clone the repo by running
```bash
git clone https://github.com/Nexoria-Development/Discord-Bot.git
```
### After cloning Fill all requirement in `.env` **(rename `.env.example` to `.env`)**, then run

```bash
npm install
```
To start your bot 

```js
node src/index.js
```

## <img src="https://cdn.discordapp.com/emojis/1036083490292244493.png" width="15px" height="15px">》Support Server
[![DiscordBanner](https://invidget.switchblade.xyz/techpoint-1016942011024158782)](https://discord.gg/techpoint-1016942011024158782)

[Support Server](https://discord.gg/techpoint-1016942011024158782) - Discord-Bot's Support Server Invite

# <img src="https://cdn.discordapp.com/emojis/1015745034076819516.png" width="25px" height="25px">》Faq
> How to get access to Developers Commands? You will have to set them up via MongoDB or run the below command.

```bash
npm run add-dev YOUR_Discord_ID
```
# Donate

 By Donating, You Will Help Me To Maintain This Project 

<img src="https://cdn.discordapp.com/emojis/809085860632985630.png" width="15px" height="15px"> 》[Sponsor](https://github.com/sponsors/Nexoria-Development)

[version-shield]: https://img.shields.io/github/package-json/v/Nexoria-Development/Discord-Bot?style=for-the-badge
[version-url]: https://github.com/brblacky/WaveMusic
[contributors-shield]: https://img.shields.io/github/contributors/Nexoria-Development/Discord-Bot.svg?style=for-the-badge
[contributors-url]: https://github.com/Nexoria-Development/Discord-Bot/graphs/contributors
[forks-shield]: https://img.shields.io/github/forks/Nexoria-Development/Discord-Bot.svg?style=for-the-badge
[forks-url]: https://github.com/Nexoria-Development/Discord-Bot/network/members
[stars-shield]: https://img.shields.io/github/stars/Nexoria-Development/Discord-Bot.svg?style=for-the-badge
[stars-url]: https://github.com/Nexoria-Development/Discord-Bot/stargazers
[issues-shield]: https://img.shields.io/github/issues/Nexoria-Development/Discord-Bot.svg?style=for-the-badge
[issues-url]: https://github.com/Nexoria-Development/Discord-Bot/issues
[license-shield]: https://img.shields.io/github/license/Nexoria-Development/Discord-Bot.svg?style=for-the-badge
[license-url]: https://github.com/Nexoria-Development/Discord-Bot/blob/master/LICENSE