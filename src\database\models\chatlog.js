const mongoose = require('mongoose');

const chatlogSchema = new mongoose.Schema({
    chatlogId: { type: String, required: true, unique: true },
    guildId: { type: String, required: true },
    channelId: { type: String, required: true },
    data: { type: Object, required: true }, // Store chatlog data here
    timestamp: { type: Date, default: Date.now },
    creatorId: { type: String, required: true }
});

module.exports = mongoose.model('Chatlog', chatlogSchema);