const Discord = require('discord.js');
const Captcha = require("@haileybot/captcha-generator");

const reactionSchema = require("../../database/models/reactionRoles");
const banSchema = require("../../database/models/userBans");
const verify = require("../../database/models/verify");
const Commands = require("../../database/models/customCommand");
const CommandsSchema = require("../../database/models/customCommandAdvanced");

module.exports = async (client, interaction) => {
    const { InteractionResponseFlags } = Discord;

    // Commands
    if (interaction.isCommand() || interaction.isUserContextMenuCommand()) {
        const banned = await banSchema.findOne({ User: interaction.user.id });
        if (banned) {
            return client.errNormal({
                error: "You have been banned by the owner of this bot",
                type: 'ephemeral'
            }, interaction);
        }

        let cmd;
        const commandName = interaction.commandName;
        const subcommandName = interaction.options.getSubcommand(false);

        if (subcommandName) {
            cmd = client.commands.get(`${commandName}-${subcommandName}`) || client.commands.get(commandName);
        } else {
            cmd = client.commands.get(commandName);
        }

        if (!cmd) {
            const cmdd = await Commands.findOne({ Guild: interaction.guild.id, Name: commandName });
            if (cmdd) return interaction.channel.send({ content: cmdd.Responce });

            const cmdx = await CommandsSchema.findOne({ Guild: interaction.guild.id, Name: commandName });
            if (cmdx) {
                if (!interaction.replied && !interaction.deferred) {
                    await interaction.deferReply({ flags: InteractionResponseFlags.Ephemeral });
                }

                if (cmdx.Action == "Normal") {
                    return interaction.editReply({ content: cmdx.Responce });
                } else if (cmdx.Action == "Embed") {
                    return client.simpleEmbed({ desc: `${cmdx.Responce}`, type: 'reply' }, interaction);
                } else if (cmdx.Action == "DM") {
                    await interaction.editReply({ content: "I have sent you something in your DMs" });
                    return interaction.user.send({ content: cmdx.Responges }).catch(() => {
                        client.errNormal({
                            error: "I can't DM you, maybe you have DM turned off!",
                            type: 'ephemeral'
                        }, interaction);
                    });
                }
            }
        }

        if (subcommandName === "help") {
            const commands = client.commands
                .filter(x => x.data.name === commandName)
                .map(x => x.data.options.map(c => '`' + c.name + '` - ' + c.description).join("\n"));

            if (!interaction.replied && !interaction.deferred) {
                await interaction.deferReply({ flags: InteractionResponseFlags.Ephemeral });
            }

            return client.embed({
                title: `❓・Help panel`,
                desc: `Get help with the commands in \`${commandName}\`\n\n${commands}`,
                type: 'reply'
            }, interaction);
        }

        if (cmd) {
            if (cmd.execute) cmd.execute(client, interaction);
            else if (cmd.run) cmd.run(client, interaction, interaction.options._hoistedOptions);
            else cmd(client, interaction, interaction.options._hoistedOptions);
        }
    }

    // Verify system
    if (interaction.isButton() && interaction.customId === "Bot_verify") {
        const data = await verify.findOne({ Guild: interaction.guild.id, Channel: interaction.channel.id });
        if (!data) {
            return client.errNormal({
                error: "Verify is disabled in this server! Or you are using the wrong channel!",
                type: 'ephemeral'
            }, interaction);
        }

        const captcha = new Captcha();
        try {
            const image = new Discord.AttachmentBuilder(captcha.JPEGStream, { name: "captcha.jpeg" });
            const msg = await interaction.reply({ files: [image], flags: InteractionResponseFlags.Ephemeral, withResponse: true });

            const filter = s => s.author.id === interaction.user.id;
            const collected = await interaction.channel.awaitMessages({ filter, max: 1 });
            const response = collected.first();

            if (response.content === captcha.value) {
                await response.delete();
                await msg.delete();

                await client.succNormal({ text: "You have been successfully verified!" }, interaction.user).catch(() => { });

                const verifyUser = interaction.guild.members.cache.get(interaction.user.id);
                verifyUser.roles.add(data.Role);
            } else {
                await response.delete();
                await msg.delete();

                const errMsg = await client.errNormal({
                    error: "You have answered the captcha incorrectly!",
                    type: 'editreply'
                }, interaction);

                setTimeout(() => { errMsg.delete().catch(() => { }); }, 2000);
            }
        } catch (error) {
            console.log(error);
        }
    }

    // Reaction roles button
    if (interaction.isButton() && interaction.customId.startsWith("reaction_button")) {
        const buttonID = interaction.customId.split("-");
        const data = await reactionSchema.findOne({ Message: interaction.message.id });
        if (!data) return;

        const [roleid] = data.Roles[buttonID[1]];
        const member = interaction.guild.members.cache.get(interaction.user.id);

        if (member.roles.cache.has(roleid)) {
            await member.roles.remove(roleid).catch(() => { });
            const replyContent = `<@&${roleid}> was removed!`;
            if (!interaction.replied && !interaction.deferred) await interaction.reply({ content: replyContent, flags: InteractionResponseFlags.Ephemeral, withResponse: true });
            else await interaction.editReply({ content: replyContent });
        } else {
            await member.roles.add(roleid).catch(() => { });
            const replyContent = `<@&${roleid}> was added!`;
            if (!interaction.replied && !interaction.deferred) await interaction.reply({ content: replyContent, flags: InteractionResponseFlags.Ephemeral, withResponse: true });
            else await interaction.editReply({ content: replyContent });
        }
    }

    // Reaction roles select
    if (interaction.isStringSelectMenu() && interaction.customId === "reaction_select") {
        const data = await reactionSchema.findOne({ Message: interaction.message.id });
        if (!data) return;

        const member = interaction.guild.members.cache.get(interaction.user.id);
        let rolesText = "";

        for (const val of interaction.values) {
            const [roleid] = data.Roles[val];
            rolesText += `<@&${roleid}> `;
            if (member.roles.cache.has(roleid)) await member.roles.remove(roleid).catch(() => { });
            else await member.roles.add(roleid).catch(() => { });
        }

        const replyContent = `I have updated the following roles for you: ${rolesText}`;
        if (!interaction.replied && !interaction.deferred) await interaction.reply({ content: replyContent, flags: InteractionResponseFlags.Ephemeral, withResponse: true });
        else await interaction.editReply({ content: replyContent });
    }

    // Tickets
    const ticketCommands = [
        "Bot_openticket",
        "Bot_closeticket",
        "Bot_claimTicket",
        "Bot_transcriptTicket",
        "Bot_openTicket",
        "Bot_deleteTicket",
        "Bot_noticeTicket"
    ];

    if (interaction.isButton() && ticketCommands.includes(interaction.customId)) {
        const filePath = `${process.cwd()}/src/commands/tickets/${interaction.customId.replace("Bot_", "").toLowerCase()}.js`;
        return require(filePath)(client, interaction);
    }
};
