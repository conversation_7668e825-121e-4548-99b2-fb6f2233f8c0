# Nexoria Bot Dashboard Setup Guide

This guide will help you set up the dashboard for your Nexoria Bot.

## Prerequisites

1. Your Discord bot should be running
2. Node.js installed
3. Your bot's Discord application credentials

## Step 1: Configure Bot Environment Variables

Add these variables to your main bot's `.env` file:

```env
# API Server Configuration (for dashboard integration)
API_PORT=4000
API_TOKEN=your-secure-api-token-here
```

## Step 2: Configure Dashboard Environment Variables

Update the `dashboard/.env` file with your bot's information:

```env
PORT=3000
BASE_URL="http://127.0.0.1:3000"

# Bot Configuration
YOUR_BOT_ID="your-discord-bot-id"
YOUR_BOT_API_URL="http://localhost:4000"
YOUR_BOT_API_TOKEN="your-secure-api-token-here"

# Discord OAuth (for dashboard login)
NEXTAUTH_SECRET="generate-a-random-string"
NEXTAUTH_URL="http://127.0.0.1:3000"
DISCORD_AUTH_ID="your-discord-app-id"
DISCORD_AUTH_SECRET="your-discord-app-secret"
```

## Step 3: Get Discord Application Credentials

1. Go to [Discord Developer Portal](https://discord.com/developers/applications)
2. Select your bot application
3. Copy the **Application ID** (use for `YOUR_BOT_ID` and `DISCORD_AUTH_ID`)
4. Go to **OAuth2** section
5. Copy the **Client Secret** (use for `DISCORD_AUTH_SECRET`)
6. Add redirect URL: `http://127.0.0.1:3000/api/auth/callback/discord`

## Step 4: Generate API Token

Generate a secure API token for communication between dashboard and bot:

```bash
# You can use this command to generate a secure token:
node -e "console.log(require('crypto').randomUUID())"
```

Use the same token for both:
- `API_TOKEN` in your bot's `.env`
- `YOUR_BOT_API_TOKEN` in dashboard's `.env`

## Step 5: Update Bot Configuration

Update `dashboard/src/core/config/bots.ts` with your bot's details:

```typescript
export const botsConfig: BotsConfig = [
    {
        name: 'Nexoria Bot',
        iconUrl: 'https://cdn.discordapp.com/avatars/YOUR_BOT_ID/YOUR_BOT_AVATAR.png',
        id: process.env['YOUR_BOT_ID']!,
        apiUrl: process.env['YOUR_BOT_API_URL']!,
        apiToken: process.env['YOUR_BOT_API_TOKEN']!
    }
]
```

## Step 6: Install Dashboard Dependencies

```bash
cd dashboard
npm install
```

## Step 7: Start the Services

1. **Start your Discord bot first:**
   ```bash
   # In the main directory
   npm start
   ```

2. **Start the dashboard:**
   ```bash
   # In the dashboard directory
   cd dashboard
   npm run dev
   ```

## Step 8: Access the Dashboard

1. Open your browser and go to `http://localhost:3000`
2. Click "Sign in with Discord"
3. Authorize the application
4. You should now see your bot's dashboard!

## Troubleshooting

### Bot API Not Responding
- Make sure your bot is running and the API server started successfully
- Check that the API_PORT (default 4000) is not blocked by firewall
- Verify the API_TOKEN matches in both .env files

### Dashboard Login Issues
- Verify DISCORD_AUTH_ID and DISCORD_AUTH_SECRET are correct
- Make sure the redirect URL is added in Discord Developer Portal
- Check that NEXTAUTH_SECRET is set

### Permission Issues
- Make sure you're an owner of the bot (check BOT_OWNERS in your bot's .env)
- The dashboard checks if you're authorized to manage the bot

## Features Available

Once set up, you'll have access to:
- ✅ Real-time bot monitoring
- ✅ Bot statistics and analytics
- ✅ Guild management
- ✅ Maintenance mode toggle
- ✅ System performance metrics
- ✅ Command usage statistics

## Next Steps

- Customize the dashboard appearance
- Add more detailed logging
- Implement command usage tracking
- Set up database backup features
