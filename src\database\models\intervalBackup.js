const mongoose = require('mongoose');

const intervalBackupSchema = new mongoose.Schema({
    guildId: { type: String, required: true, unique: true },
    userId: { type: String, required: true },
    interval: { type: String, required: true }, // e.g., "24h", "4h"
    messageCount: { type: Number, default: 0 },
    lastBackup: { type: Date, default: Date.now }
});

module.exports = mongoose.model('IntervalBackup', intervalBackupSchema);