# Dokploy Environment Configuration
# Copy this to your Dokploy environment variables

# ===== BOT CONFIGURATION =====
DISCORD_TOKEN=your_discord_bot_token
MONGO_TOKEN=your_mongodb_connection_string
DISCORD_ID=your_discord_bot_id
BOT_OWNERS=your_discord_user_id

# ===== API SERVER =====
API_PORT=4000
API_TOKEN=generate_secure_random_token_here

# ===== DASHBOARD CONFIGURATION =====
PORT=3000
BASE_URL=https://your-dokploy-domain.com
NEXTAUTH_URL=https://your-dokploy-domain.com
NEXTAUTH_SECRET=generate_another_random_string

# ===== DISCORD OAUTH =====
DISCORD_AUTH_ID=your_discord_bot_id
DISCORD_AUTH_SECRET=your_discord_app_secret

# ===== BOT API CONNECTION =====
YOUR_BOT_ID=your_discord_bot_id
YOUR_BOT_API_URL=http://localhost:4000
YOUR_BOT_API_TOKEN=same_as_api_token_above

# ===== OPTIONAL VARIABLES =====
WEBHOOK_ID=your_webhook_id
WEBHOOK_TOKEN=your_webhook_token
GIPHY_TOKEN=your_giphy_token
TOPGG_TOKEN=your_topgg_token
SPOTIFY_CLIENT_ID=your_spotify_client_id
SPOTIFY_CLIENT_SECRET=your_spotify_client_secret
