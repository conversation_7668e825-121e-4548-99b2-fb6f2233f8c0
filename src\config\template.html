<!DOCTYPE html>
<html lang="en">

<head>
    <title>{{TITLE}}</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width">

    <style>
        @font-face {
            font-family: Whitney;
            src: url(https://cdn.jsdelivr.net/gh/Tyrrrz/DiscordFonts@master/whitney-300.woff);
            font-weight: 300;
        }

        @font-face {
            font-family: Whitney;
            src: url(https://cdn.jsdelivr.net/gh/Tyrrrz/DiscordFonts@master/whitney-400.woff);
            font-weight: 400;
        }

        @font-face {
            font-family: Whitney;
            src: url(https://cdn.jsdelivr.net/gh/Tyrrrz/DiscordFonts@master/whitney-500.woff);
            font-weight: 500;
        }

        @font-face {
            font-family: Whitney;
            src: url(https://cdn.jsdelivr.net/gh/Tyrrrz/DiscordFonts@master/whitney-600.woff);
            font-weight: 600;
        }

        @font-face {
            font-family: Whitney;
            src: url(https://cdn.jsdelivr.net/gh/Tyrrrz/DiscordFonts@master/whitney-700.woff);
            font-weight: 700;
        }

        body {
            background-color: #36393e;
            color: #dcddde;
            font-family: "Whitney", "Helvetica Neue", Helvetica, Arial, sans-serif;
            font-size: 17px;
            font-weight: 400;
        }

        a {
            color: #00aff4;
            text-decoration: none;
        }

        a:hover {
            text-decoration: underline;
        }

        img {
            object-fit: contain;
        }

        .markdown {
            max-width: 100%;
            line-height: 1.3;
            overflow-wrap: break-word;
        }

        .preserve-whitespace {
            white-space: pre-wrap;
        }

        .spoiler-text {
            background-color: rgba(255, 255, 255, 0.1);
            border-radius: 3px;
        }

        .spoiler-text--hidden {
            cursor: pointer;
            background-color: #202225;
            color: rgba(0, 0, 0, 0);
        }

        .spoiler-text--hidden:hover {
            background-color: rgba(32, 34, 37, 0.8);
        }

        .spoiler-text--hidden::selection {
            color: rgba(0, 0, 0, 0);
        }

        .quote {
            display: flex;
            margin: 0.05em 0;
        }

        .quote::before {
            content: "";
            margin-right: 0.5em;
            border: 2px solid #4f545c;
            border-radius: 3px;
        }

        .pre {
            background-color: #2f3136;
            font-family: "Consolas", "Courier New", Courier, monospace;
        }

        .pre--multiline {
            margin-top: 0.25em;
            padding: 0.5em;
            border: 2px solid #282b30;
            border-radius: 5px;
            color: #b9bbbe;
        }

        .pre--multiline.hljs {
            background-color: #2f3136;
            color: #b9bbbe;
        }

        .pre--inline {
            padding: 2px;
            border-radius: 3px;
            font-size: 0.85em;
        }

        .mention {
            border-radius: 3px;
            padding: 0 2px;
            color: #7289da;
            background-color: rgba(114, 137, 218, .1);
            font-weight: 500;
        }

        .timestamp {
            border-radius: 3px;
            padding: 0 2px;
            background-color: rgba(255, 255, 255, 0.06);
        }

        .emoji {
            width: 1.325em;
            height: 1.325em;
            margin: 0 0.06em;
            vertical-align: -0.4em;
        }

        .emoji--small {
            width: 1em;
            height: 1em;
        }

        .emoji--large {
            width: 2.8em;
            height: 2.8em;
        }

        .preamble {
            display: grid;
            margin: 0 0.3em 0.6em 0.3em;
            max-width: 100%;
            grid-template-columns: auto 1fr;
        }

        .preamble__guild-icon-container {
            grid-column: 1;
        }

        .preamble__guild-icon {
            max-width: 88px;
            max-height: 88px;
        }

        .preamble__entries-container {
            grid-column: 2;
            margin-left: 0.6em;
        }

        .preamble__entry {
            font-size: 1.4em;
            color: #ffffff;
        }

        .preamble__entry--small {
            font-size: 1em;
        }

        .chatlog {
            max-width: 100%;
        }

        .chatlog__message-group {
            display: grid;
            margin: 0 0.6em;
            padding: 0.9em 0;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            grid-template-columns: auto 1fr;
        }

        .chatlog__reference-symbol {
            grid-column: 1;
            margin: 8px 4px 4px 18px;
            border-left: 2px solid #4f545c;
            border-top: 2px solid #4f545c;
            border-radius: 8px 0 0 0;
        }

        .chatlog__reference {
            display: flex;
            grid-column: 2;
            margin-bottom: 0.25em;
            font-size: 0.875em;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            align-items: center;
            color: #b5b6b8;
        }

        .chatlog__reference-avatar {
            border-radius: 50%;
            width: 16px;
            height: 16px;
            margin-right: 0.25em;
        }

        .chatlog__reference-name {
            margin-right: 0.3em;
            font-weight: 600;
        }

        .chatlog__reference-content {
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .chatlog__reference-content a:hover {
            text-decoration: none;
        }

        .chatlog__reference-link {
            cursor: pointer;
            color: #b5b6b8;
        }

        .chatlog__reference-link * {
            display: inline;
            pointer-events: none;
        }

        .chatlog__reference-link:hover {
            color: #ffffff;
        }

        .chatlog__reference-link:hover *:not(.spoiler-text) {
            color: #ffffff;
        }

        .chatlog__reference-edited-timestamp {
            margin-left: 0.25em;
            font-size: 0.8em;
            color: rgba(255, 255, 255, 0.2);
        }

        .chatlog__author-avatar-container {
            grid-column: 1;
            width: 40px;
            height: 40px;
            margin-right: 16px;
        }

        .chatlog__author-avatar {
            border-radius: 50%;
            width: 40px;
            height: 40px;
        }

        .chatlog__messages {
            grid-column: 2;
            min-width: 50%;
        }

        .chatlog__author-name {
            font-weight: 500;
            color: #ffffff;
        }

        .chatlog__timestamp {
            margin-left: 0.3em;
            font-size: 0.75em;
            color: rgba(255, 255, 255, 0.2);
        }

        .chatlog__message {
            padding: 0.1em 0.3em;
            margin: 0 -0.3em;
            background-color: transparent;
            transition: background-color 1s ease;
        }

        .chatlog__message--highlighted {
            background-color: rgba(114, 137, 218, 0.2);
        }

        .chatlog__message--pinned {
            background-color: rgba(249, 168, 37, 0.05);
        }

        .chatlog__content {
            font-size: 0.95em;
            word-wrap: break-word;
        }

        .chatlog__edited-timestamp {
            margin-left: 0.15em;
            font-size: 0.8em;
        }

        .chatlog__attachment {
            display: inline-block;
            position: relative;
            margin-top: 0.3em;
            border-radius: 3px;
            overflow: hidden;
        }

        .chatlog__attachment--hidden {
            cursor: pointer;
            box-shadow: 0 0 1px 1px rgba(0, 0, 0, 0.1);
        }

        .chatlog__attachment--hidden * {
            pointer-events: none;
        }

        .chatlog__attachment-spoiler-caption {
            display: none;
            z-index: 999;
            position: absolute;
            left: 50%;
            top: 50%;
            transform: translate(-50%, -50%);
            padding: 0.4em 0.8em;
            background-color: rgba(0, 0, 0, 0.9);
            border-radius: 20px;
            color: #dcddde;
            font-size: 0.9em;
            font-weight: 600;
            letter-spacing: 0.05em;
        }

        .chatlog__attachment--hidden .chatlog__attachment-spoiler-caption {
            display: block;
        }

        .chatlog__attachment--hidden:hover .chatlog__attachment-spoiler-caption {
            color: #fff;
        }

        .chatlog__attachment-media {
            vertical-align: top;
            max-width: 45vw;
            max-height: 500px;
            border-radius: 3px;
        }

        .chatlog__attachment--hidden .chatlog__attachment-media {
            filter: blur(44px);
        }

        .chatlog__attachment-generic {
            width: 100%;
            max-width: 520px;
            height: 40px;
            padding: 10px;
            background-color: #2f3136;
            border: 1px solid #292b2f;
            border-radius: 3px;
            overflow: hidden;
        }

        .chatlog__attachment--hidden .chatlog__attachment-generic {
            filter: blur(44px);
        }

        .chatlog__attachment-generic-icon {
            float: left;
            width: 30px;
            height: 100%;
            margin-right: 10px;
        }

        .chatlog__attachment-generic-size {
            color: #72767d;
            font-size: 12px;
        }

        .chatlog__attachment-generic-name {
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
        }

        .chatlog__edited-timestamp {
            color: rgba(255, 255, 255, 0.2);
        }

        .chatlog__embed {
            display: flex;
            margin-top: 0.3em;
            max-width: 520px;
        }

        .chatlog__embed-color-pill {
            flex-shrink: 0;
            width: 0.25em;
            border-top-left-radius: 3px;
            border-bottom-left-radius: 3px;
        }

        .chatlog__embed-color-pill--default {
            background-color: #202225;
        }

        .chatlog__embed-content-container {
            display: flex;
            flex-direction: column;
            padding: 0.5em 0.6em;
            background-color: rgba(46, 48, 54, 0.3);
            border: 1px solid rgba(46, 48, 54, 0.6);
            border-top-right-radius: 3px;
            border-bottom-right-radius: 3px;
        }

        .chatlog__embed-content {
            display: flex;
            width: 100%;
        }

        .chatlog__embed-text {
            flex: 1;
        }

        .chatlog__embed-author {
            display: flex;
            margin-bottom: 0.3em;
            align-items: center;
        }

        .chatlog__embed-author-icon {
            margin-right: 0.5em;
            width: 20px;
            height: 20px;
            border-radius: 50%;
        }

        .chatlog__embed-author-name {
            font-size: 0.875em;
            font-weight: 600;
            color: #ffffff
        }

        .chatlog__embed-author-name-link {
            color: #ffffff;
        }

        .chatlog__embed-title {
            margin-bottom: 0.2em;
            font-size: 0.875em;
            font-weight: 600;
            color: #ffffff;
        }

        .chatlog__embed-description {
            font-weight: 500;
            font-size: 0.85em;
            color: #dcddde;
        }

        .chatlog__embed-fields {
            display: flex;
            flex-wrap: wrap;
            gap: 0 0.5em;
        }

        .chatlog__embed-field {
            flex: 0;
            min-width: 100%;
            max-width: 506px;
            padding-top: 0.6em;
            font-size: 0.875em;
        }

        .chatlog__embed-field--inline {
            flex: 1;
            flex-basis: auto;
            min-width: 150px;
        }

        .chatlog__embed-field-name {
            margin-bottom: 0.2em;
            font-weight: 600;
            color: #ffffff;
        }

        .chatlog__embed-field-value {
            font-weight: 500;
            color: #dcddde;
        }

        .chatlog__embed-thumbnail {
            flex: 0;
            margin-left: 1.2em;
            max-width: 80px;
            max-height: 80px;
            border-radius: 3px;
        }

        .chatlog__embed-image-container {
            margin-top: 0.6em;
        }

        .chatlog__embed-image {
            max-width: 500px;
            max-height: 400px;
            border-radius: 3px;
        }

        .chatlog__embed-footer {
            margin-top: 0.6em;
            color: #dcddde;
        }

        .chatlog__embed-footer-icon {
            margin-right: 0.2em;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            vertical-align: middle;
        }

        .chatlog__embed-footer-text {
            vertical-align: middle;
            font-size: 0.75em;
            font-weight: 500;
        }

        .chatlog__embed-plainimage {
            vertical-align: top;
            max-width: 45vw;
            max-height: 500px;
            border-radius: 3px;
        }

        .chatlog__embed-spotify {
            border: 0;
        }

        .chatlog__embed-youtube-container {
            margin-top: 0.6em;
        }

        .chatlog__embed-youtube {
            border: 0;
            border-radius: 3px;
        }

        .chatlog__reactions {
            display: flex;
        }

        .chatlog__reaction {
            display: flex;
            align-items: center;
            margin: 0.35em 0.1em 0.1em 0.1em;
            padding: 0.2em 0.35em;
            background-color: rgba(255, 255, 255, 0.05);
            border-radius: 3px;
        }

        .chatlog__reaction-count {
            min-width: 9px;
            margin-left: 0.35em;
            font-size: 0.875em;
            color: rgba(255, 255, 255, 0.3);
        }

        .chatlog__bot-tag {
            position: relative;
            top: -.1em;
            margin-left: 0.3em;
            padding: 0.05em 0.3em;
            border-radius: 3px;
            line-height: 1.3;
            background-color: #5865F2;
            color: #ffffff;
            font-size: 0.625em;
            font-weight: 500;
        }

        .postamble {
            margin: 1.4em 0.3em 0.6em 0.3em;
            padding: 1em;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
        }

        .postamble__entry {
            color: #ffffff;
        }
    </style>

    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/9.15.6/styles/solarized-dark.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/9.15.6/highlight.min.js"></script>
    <script>
        // highlightjs done in exporthtml.js because doing it here caused newline issues.
        // document.addEventListener('DOMContentLoaded', () => {
        //     document.querySelectorAll('.pre--multiline').forEach(block => hljs.highlightBlock(block));
        // });
    </script>

    <script>
        function scrollToMessage(event, id) {
            var element = document.getElementById('message-' + id);

            if (element) {
                event.preventDefault();

                element.classList.add('chatlog__message--highlighted');

                window.scrollTo({
                    top: element.getBoundingClientRect().top - document.body.getBoundingClientRect().top - (window.innerHeight / 2),
                    behavior: 'smooth'
                });

                window.setTimeout(function () {
                    element.classList.remove('chatlog__message--highlighted');
                }, 2000);
            }
        }

        function showSpoiler(event, element) {
            if (element && element.classList.contains('spoiler-text--hidden')) {
                event.preventDefault();
                element.classList.remove('spoiler-text--hidden');
            }
            if (element && element.classList.contains('chatlog__attachment--hidden')) {
                event.preventDefault();
                element.classList.remove('chatlog__attachment--hidden');
            }
        }
    </script>

    <svg style="display: none">
        <symbol id="icon-attachment" viewBox="0 0 720 960">
            <path fill="#f4f5fb"
                d="M50,935a25,25,0,0,1-25-25V50A25,25,0,0,1,50,25H519.6L695,201.32V910a25,25,0,0,1-25,25Z" />
            <path fill="#7789c4"
                d="M509.21,50,670,211.63V910H50V50H509.21M530,0H50A50,50,0,0,0,0,50V910a50,50,0,0,0,50,50H670a50,50,0,0,0,50-50h0V191Z" />
            <path fill="#f4f5fb"
                d="M530,215a25,25,0,0,1-25-25V50a25,25,0,0,1,16.23-23.41L693.41,198.77A25,25,0,0,1,670,215Z" />
            <path fill="#7789c4"
                d="M530,70.71,649.29,190H530V70.71M530,0a50,50,0,0,0-50,50V190a50,50,0,0,0,50,50H670a50,50,0,0,0,50-50Z" />
        </symbol>
    </svg>
</head>

<body>

    <div class="preamble">
        <div class="preamble__guild-icon-container">
            <img class="preamble__guild-icon" src="https://discord.com/assets/f9bb9c4af2b9c32a2c5ee0014661546d.png"
                alt="Guild icon" loading="lazy">
        </div>
        <div class="preamble__entries-container">
            <div class="preamble__entry" id="guildname">Guild Name Here</div>
            <div class="preamble__entry" id="ticketname">Channel Name Here</div>
        </div>
    </div>

    <div class="chatlog" id="chatlog">

    </div>

</body>

</html>