const Discord = require('discord.js');
const { SlashCommandBuilder } = require('@discordjs/builders');

const Counting = require("../../database/models/countChannel");
const GTN = require("../../database/models/guessNumber");
const GTW = require("../../database/models/guessWord");
const WordSnake = require("../../database/models/wordsnake");

module.exports = {
    data: new SlashCommandBuilder()
        .setName('games')
        .setDescription('Setup game channels.')
        .addStringOption(option =>
            option.setName('setup')
                .setDescription('The game channel to set up.')
                .setRequired(true)
                .addChoices(
                    { name: 'Counting', value: 'counting' },
                    { name: 'Guess the number', value: 'gtn' },
                    { name: 'Guess the word', value: 'gtw' },
                    { name: 'Word Snake', value: 'wordsnake' }
                )),
    async execute(client, interaction, args) {
        const choice = interaction.options.getString('setup');

        if (choice == "counting") {
            interaction.guild.channels.create({
                name: "counting",
                type: Discord.ChannelType.GuildText
            }).then((ch) => {
                client.embed({
                    title: `🔢・Counting`,
                    desc: `This is the start of counting! The first number is **1**`
                }, ch)

                client.createChannelSetup(Counting, ch, interaction)
            })
        }

        if (choice == "gtn") {
            interaction.guild.channels.create({
                name:"guess-the-number",
                type: Discord.ChannelType.GuildText
            }).then((ch) => {
                client.embed({
                    title: `🔢・Guess the number`,
                    desc: `Guess the number between **1** and **10.000**!`
                }, ch)

                client.createChannelSetup(GTN, ch, interaction)
            })
        }

        if (choice == "gtw") {
            interaction.guild.channels.create({
                name: "guess-the-word",
                type: Discord.ChannelType.GuildText
            }).then((ch) => {
                var word = "start";
                var shuffled = word.split('').sort(function () { return 0.5 - Math.random() }).join('');

                client.embed({
                    title: `💬・Guess the word`,
                    desc: `Put the letters in the right position!`,
                    fields: [
                        {
                            name: `🔀┆Word`,
                            value: `${shuffled.toLowerCase()}`
                        }
                    ],
                }, ch)

                client.createChannelSetup(GTW, ch, interaction)
            })
        }

        if (choice == "wordsnake") {
            interaction.guild.channels.create({
                name: "word-snake",
                type: Discord.ChannelType.GuildText
            }).then((ch) => {
                client.createChannelSetup(WordSnake, ch, interaction)
            })
        }
    },
};

