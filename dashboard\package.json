{"name": "tscord-dashboard", "version": "2.4.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "generate": "plop --plopfile ./cli/plopfile.js"}, "dependencies": {"@chakra-ui/react": "^2.8.2", "@chakra-ui/theme-tools": "^2.1.2", "@emotion/react": "^11.11.3", "@emotion/styled": "^11.11.0", "@fontsource/dm-sans": "^5.0.18", "apexcharts": "^3.45.2", "axios": "^1.6.7", "color-alpha": "^1.1.3", "dayjs": "^1.11.10", "framer-motion": "^11.0.3", "javascript-time-ago": "^2.5.9", "lodash": "^4.17.21", "next": "^14.1.0", "next-auth": "^4.24.5", "react": "18.2.0", "react-apexcharts": "^1.4.1", "react-dom": "18.2.0", "react-icons": "^5.0.1", "react-intersection-observer": "^9.8.0", "react-string-replace": "^1.1.1", "react-table": "^7.8.0", "sass": "^1.70.0", "strip-ansi": "^7.1.0", "swr": "^2.2.4"}, "devDependencies": {"@types/lodash": "^4.14.202", "@types/node": "20.11.17", "@types/react": "18.2.55", "@types/react-dom": "18.2.19", "@types/react-table": "^7.7.19", "eslint": "8.47.0", "eslint-config-next": "14.1.0", "plop": "^4.0.1", "typescript": "5.1.6"}, "volta": {"node": "20.11.0"}}