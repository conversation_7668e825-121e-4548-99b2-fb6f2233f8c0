// Simple test script to verify API endpoints
const http = require('http');

const API_PORT = process.env.API_PORT || 4000;
const API_TOKEN = process.env.API_TOKEN || 'test-token';

function testEndpoint(path, expectedStatus = 200) {
    return new Promise((resolve, reject) => {
        const options = {
            hostname: 'localhost',
            port: API_PORT,
            path: path,
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${API_TOKEN}`,
                'Content-Type': 'application/json'
            }
        };

        const req = http.request(options, (res) => {
            let data = '';
            
            res.on('data', (chunk) => {
                data += chunk;
            });
            
            res.on('end', () => {
                try {
                    const result = {
                        path,
                        status: res.statusCode,
                        expected: expectedStatus,
                        success: res.statusCode === expectedStatus,
                        data: data ? JSON.parse(data) : null
                    };
                    resolve(result);
                } catch (error) {
                    resolve({
                        path,
                        status: res.statusCode,
                        expected: expectedStatus,
                        success: false,
                        error: error.message,
                        rawData: data
                    });
                }
            });
        });

        req.on('error', (error) => {
            reject({ path, error: error.message });
        });

        req.setTimeout(5000, () => {
            req.destroy();
            reject({ path, error: 'Request timeout' });
        });

        req.end();
    });
}

async function runTests() {
    console.log('🧪 Testing API endpoints...\n');
    
    const endpoints = [
        '/health',
        '/api/bot',
        '/api/stats',
        '/api/guilds',
        '/api/commands',
        '/api/maintenance',
        '/api/system',
        '/bot/devs',
        '/stats/totals',
        '/health/monitoring'
    ];

    const results = [];
    
    for (const endpoint of endpoints) {
        try {
            console.log(`Testing ${endpoint}...`);
            const result = await testEndpoint(endpoint);
            results.push(result);
            
            if (result.success) {
                console.log(`✅ ${endpoint} - Status: ${result.status}`);
            } else {
                console.log(`❌ ${endpoint} - Expected: ${result.expected}, Got: ${result.status}`);
            }
        } catch (error) {
            console.log(`❌ ${endpoint} - Error: ${error.error}`);
            results.push({ ...error, success: false });
        }
    }
    
    console.log('\n📊 Test Summary:');
    const successful = results.filter(r => r.success).length;
    const total = results.length;
    console.log(`✅ Successful: ${successful}/${total}`);
    console.log(`❌ Failed: ${total - successful}/${total}`);
    
    if (successful === total) {
        console.log('\n🎉 All tests passed! Your API server is working correctly.');
    } else {
        console.log('\n⚠️  Some tests failed. Check the API server configuration.');
    }
}

// Check if API server is running
console.log(`Checking API server on port ${API_PORT}...`);
runTests().catch(error => {
    console.error('❌ Failed to run tests:', error);
    console.log('\n💡 Make sure your Discord bot is running with the API server enabled.');
});
