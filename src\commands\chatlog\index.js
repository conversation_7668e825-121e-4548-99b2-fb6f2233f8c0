const { SlashCommandBuilder, MessageFlags } = require('discord.js');
const Chatlog = require('../../database/models/chatlog');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('chatlog')
        .setDescription('Manage chatlogs.')
        .addSubcommand(subcommand =>
            subcommand
                .setName('create')
                .setDescription('Generates and saves a chat log of a given channel (or all channels).')
                .addChannelOption(option =>
                    option.setName('channel')
                        .setDescription('The channel to create a chatlog for. Leave empty for all channels.')
                        .setRequired(false)
                )
        )
        .addSubcommand(subcommand =>
            subcommand
                .setName('list')
                .setDescription('Lists your saved chatlogs with their IDs.')
        )
        .addSubcommand(subcommand =>
            subcommand
                .setName('view')
                .setDescription('Views the content of a saved chatlog.')
                .addStringOption(option =>
                    option.setName('chatlog_id')
                        .setDescription('The ID of the chatlog to view.')
                        .setRequired(true)
                )
        ),
    async execute(interaction) {
        const subcommand = interaction.options.getSubcommand();

        if (subcommand === 'create') {
            await interaction.deferReply({ flags: MessageFlags.Ephemeral });
            const channel = interaction.options.getChannel('channel') || interaction.channel;

            try {
                // Fetch messages (example: last 100 messages)
                const messages = await channel.messages.fetch({ limit: 100 });
                const chatlogData = messages.map(msg => ({
                    author: msg.author.tag,
                    content: msg.content,
                    timestamp: msg.createdAt,
                    attachments: msg.attachments.map(att => att.url)
                }));

                const newChatlog = new Chatlog({
                    chatlogId: `${interaction.guild.id}-${channel.id}-${Date.now()}`,
                    guildId: interaction.guild.id,
                    channelId: channel.id,
                    data: chatlogData,
                    creatorId: interaction.user.id
                });

                await newChatlog.save();
                await interaction.editReply(`Chatlog created successfully for #${channel.name} with ID: \`${newChatlog.chatlogId}\`.`);
            } catch (error) {
                console.error('Error creating chatlog:', error);
                await interaction.editReply('Failed to create chatlog. Please try again later.');
            }

        } else if (subcommand === 'list') {
            await interaction.deferReply({ flags: MessageFlags.Ephemeral });

            try {
                const chatlogs = await Chatlog.find({ creatorId: interaction.user.id, guildId: interaction.guild.id });

                if (chatlogs.length === 0) {
                    return await interaction.editReply('No chatlogs found for you in this server.');
                }

                let replyMessage = '**Your Chatlogs for this Server:**\n';
                chatlogs.forEach((chatlog, index) => {
                    replyMessage += `\n${index + 1}. Channel: <#${chatlog.channelId}> | ID: \`${chatlog.chatlogId}\` (Created: ${chatlog.timestamp.toLocaleString()})`;
                });

                await interaction.editReply(replyMessage);
            } catch (error) {
                console.error('Error listing chatlogs:', error);
                await interaction.editReply('Failed to retrieve chatlogs. Please try again later.');
            }
        } else if (subcommand === 'view') {
            await interaction.deferReply({ flags: MessageFlags.Ephemeral });
            const chatlogId = interaction.options.getString('chatlog_id');

            try {
                const chatlog = await Chatlog.findOne({ creatorId: interaction.user.id, chatlogId: chatlogId });

                if (!chatlog) {
                    return await interaction.editReply(`Chatlog with ID \`${chatlogId}\` not found or does not belong to you.`);
                }

                let chatlogContent = `**Chatlog for <#${chatlog.channelId}> (Server: ${chatlog.guildId}) - Created: ${chatlog.timestamp.toLocaleString()}**\n\n`;
                chatlog.data.forEach(msg => {
                    chatlogContent += `[${new Date(msg.timestamp).toLocaleTimeString()}] ${msg.author}: ${msg.content}\n`;
                    if (msg.attachments && msg.attachments.length > 0) {
                        chatlogContent += `  Attachments: ${msg.attachments.join(', ')}\n`;
                    }
                });

                // Discord has a message character limit (2000). Send in chunks if too long.
                if (chatlogContent.length > 1900) {
                    await interaction.editReply(`Chatlog content is too long to display directly. Sending as a file... (Not yet implemented)`);
                    // In a real scenario, you would create a text file and send it.
                } else {
                    await interaction.editReply(chatlogContent);
                }

            } catch (error) {
                console.error('Error viewing chatlog:', error);
                await interaction.editReply('Failed to view chatlog. Please try again later.');
            }
        }
    },
};