const http = require('http');
const url = require('url');
const crypto = require('crypto');

class APIServer {
    constructor(client) {
        this.client = client;
        this.port = process.env.API_PORT || 4000;
        this.apiToken = process.env.API_TOKEN || crypto.randomUUID();

        this.server = http.createServer((req, res) => this.handleRequest(req, res));
    }

    // Helper method to parse JSON body
    parseBody(req) {
        return new Promise((resolve, reject) => {
            let body = '';
            req.on('data', chunk => {
                body += chunk.toString();
            });
            req.on('end', () => {
                try {
                    resolve(body ? JSON.parse(body) : {});
                } catch (error) {
                    reject(error);
                }
            });
        });
    }

    // Helper method to send JSON response
    sendJSON(res, data, statusCode = 200) {
        res.writeHead(statusCode, {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
            'Access-Control-Allow-Headers': 'Origin, X-Requested-With, Content-Type, Accept, Authorization'
        });
        res.end(JSON.stringify(data));
    }

    // Authentication check
    isAuthenticated(req) {
        const authHeader = req.headers.authorization;
        const token = authHeader?.replace('Bearer ', '');
        return token === this.apiToken;
    }

    async handleRequest(req, res) {
        const parsedUrl = url.parse(req.url, true);
        const path = parsedUrl.pathname;
        const method = req.method;

        // Handle CORS preflight
        if (method === 'OPTIONS') {
            this.sendJSON(res, {}, 200);
            return;
        }

        try {
            // Health check endpoint (no auth required)
            if (path === '/health' && method === 'GET') {
                this.sendJSON(res, { status: 'ok', timestamp: new Date().toISOString() });
                return;
            }

            // All API endpoints require authentication
            if (path.startsWith('/api/')) {
                if (!this.isAuthenticated(req)) {
                    this.sendJSON(res, { error: 'Unauthorized' }, 401);
                    return;
                }
            }

            // Bot info endpoint
            if (path === '/api/bot' && method === 'GET') {
                const botInfo = {
                    id: this.client.user.id,
                    username: this.client.user.username,
                    discriminator: this.client.user.discriminator,
                    avatar: this.client.user.displayAvatarURL(),
                    status: this.client.presence?.status || 'online',
                    uptime: this.client.uptime,
                    guilds: this.client.guilds.cache.size,
                    users: this.client.users.cache.size,
                    channels: this.client.channels.cache.size,
                    commands: this.client.commands.size,
                    ping: this.client.ws.ping,
                    memory: process.memoryUsage(),
                    version: require('../../package.json').version
                };

                this.sendJSON(res, botInfo);
                return;
            }

            // Bot stats endpoint
            if (path === '/api/stats' && method === 'GET') {
                const stats = {
                    guilds: this.client.guilds.cache.size,
                    users: this.client.users.cache.size,
                    channels: this.client.channels.cache.size,
                    commands: this.client.commands.size,
                    uptime: this.client.uptime,
                    ping: this.client.ws.ping,
                    memory: process.memoryUsage(),
                    shards: this.client.shard ? this.client.shard.count : 1
                };

                this.sendJSON(res, stats);
                return;
            }

            // Guilds list endpoint
            if (path === '/api/guilds' && method === 'GET') {
                const guilds = this.client.guilds.cache.map(guild => ({
                    id: guild.id,
                    name: guild.name,
                    icon: guild.iconURL(),
                    memberCount: guild.memberCount,
                    ownerId: guild.ownerId,
                    createdAt: guild.createdAt,
                    joinedAt: guild.joinedAt
                }));

                this.sendJSON(res, guilds);
                return;
            }

            // Specific guild info endpoint
            if (path.startsWith('/api/guilds/') && method === 'GET') {
                const guildId = path.split('/')[3];
                const guild = this.client.guilds.cache.get(guildId);

                if (!guild) {
                    this.sendJSON(res, { error: 'Guild not found' }, 404);
                    return;
                }

                const guildInfo = {
                    id: guild.id,
                    name: guild.name,
                    icon: guild.iconURL(),
                    banner: guild.bannerURL(),
                    description: guild.description,
                    memberCount: guild.memberCount,
                    ownerId: guild.ownerId,
                    createdAt: guild.createdAt,
                    joinedAt: guild.joinedAt,
                    features: guild.features,
                    channels: guild.channels.cache.size,
                    roles: guild.roles.cache.size,
                    emojis: guild.emojis.cache.size
                };

                this.sendJSON(res, guildInfo);
                return;
            }

            // Commands list endpoint
            if (path === '/api/commands' && method === 'GET') {
                const commands = Array.from(this.client.commands.values()).map(command => ({
                    name: command.data?.name || command.name,
                    description: command.data?.description || command.description,
                    category: command.category || 'Unknown'
                }));

                this.sendJSON(res, commands);
                return;
            }

            // Maintenance mode endpoints
            if (path === '/api/maintenance' && method === 'GET') {
                this.sendJSON(res, {
                    maintenance: this.client.maintenance || false
                });
                return;
            }

            if (path === '/api/maintenance' && method === 'POST') {
                const body = await this.parseBody(req);
                const { enabled } = body;
                this.client.maintenance = Boolean(enabled);

                this.sendJSON(res, {
                    maintenance: this.client.maintenance,
                    message: `Maintenance mode ${this.client.maintenance ? 'enabled' : 'disabled'}`
                });
                return;
            }

            // System info endpoint
            if (path === '/api/system' && method === 'GET') {
                const system = {
                    platform: process.platform,
                    arch: process.arch,
                    nodeVersion: process.version,
                    memory: process.memoryUsage(),
                    uptime: process.uptime(),
                    pid: process.pid
                };

                this.sendJSON(res, system);
                return;
            }

            // Dashboard specific endpoints

            // Bot developers/owners endpoint
            if (path === '/bot/devs' && method === 'GET') {
                const owners = this.client.config?.owners || [];
                this.sendJSON(res, owners);
                return;
            }

            // Stats totals endpoint
            if (path === '/stats/totals' && method === 'GET') {
                const stats = {
                    totalCommands: this.client.commands.size,
                    totalGuilds: this.client.guilds.cache.size,
                    totalUsers: this.client.users.cache.size,
                    totalActiveUsers: this.client.users.cache.filter(user => !user.bot).size
                };

                this.sendJSON(res, { stats });
                return;
            }

            // Health monitoring endpoint
            if (path === '/health/monitoring' && method === 'GET') {
                const monitoring = [{
                    fetchedAt: new Date(),
                    botStatus: {
                        online: this.client.isReady(),
                        uptime: this.client.uptime,
                        maintenance: this.client.maintenance || false
                    },
                    host: {
                        cpu: 0, // Would need additional package for real CPU usage
                        memory: {
                            freeMemPercentage: Math.round((1 - (process.memoryUsage().heapUsed / process.memoryUsage().heapTotal)) * 100),
                            usedMemPercentage: Math.round((process.memoryUsage().heapUsed / process.memoryUsage().heapTotal) * 100)
                        },
                        os: process.platform,
                        uptime: process.uptime(),
                        hostname: require('os').hostname(),
                        platform: process.platform
                    },
                    pid: {
                        memory: {
                            usedInMb: Math.round(process.memoryUsage().heapUsed / 1024 / 1024),
                            percentage: Math.round((process.memoryUsage().heapUsed / process.memoryUsage().heapTotal) * 100)
                        },
                        cpu: 0, // Would need additional package for real CPU usage
                        ppid: process.ppid || 0,
                        pid: process.pid,
                        ctime: 0,
                        elapsed: process.uptime(),
                        timestamp: Date.now()
                    },
                    latency: {
                        ping: this.client.ws.ping
                    }
                }];

                this.sendJSON(res, monitoring);
                return;
            }

            // Health logs endpoint
            if (path === '/health/logs' && method === 'GET') {
                // Return empty array for now - would need to implement log storage
                this.sendJSON(res, []);
                return;
            }

            // Bot maintenance endpoint (GET)
            if (path === '/bot/maintenance' && method === 'GET') {
                this.sendJSON(res, {
                    maintenance: this.client.maintenance || false
                });
                return;
            }

            // Stats endpoints for dashboard charts
            if (path === '/stats/commands/top' && method === 'GET') {
                // Mock data - would need to implement command usage tracking
                this.sendJSON(res, []);
                return;
            }

            if (path === '/stats/guilds/top' && method === 'GET') {
                const topGuilds = this.client.guilds.cache
                    .sort((a, b) => b.memberCount - a.memberCount)
                    .first(10)
                    .map(guild => ({
                        name: guild.name,
                        memberCount: guild.memberCount,
                        id: guild.id
                    }));

                this.sendJSON(res, topGuilds);
                return;
            }

            if (path === '/stats/interaction/last' && method === 'GET') {
                // Mock data - would need to implement interaction tracking
                this.sendJSON(res, { createdAt: new Date() });
                return;
            }

            if (path === '/stats/guilds/last' && method === 'GET') {
                // Get the most recently joined guild
                const lastGuild = this.client.guilds.cache
                    .sort((a, b) => b.joinedTimestamp - a.joinedTimestamp)
                    .first();

                if (lastGuild) {
                    this.sendJSON(res, {
                        name: lastGuild.name,
                        joinedAt: lastGuild.joinedAt,
                        id: lastGuild.id
                    });
                } else {
                    this.sendJSON(res, null);
                }
                return;
            }

            if (path === '/stats/commands/usage' && method === 'GET') {
                // Mock data - would need to implement command usage tracking
                this.sendJSON(res, []);
                return;
            }

            if (path === '/stats/users/activity' && method === 'GET') {
                const totalUsers = this.client.users.cache.size;
                const activeUsers = this.client.users.cache.filter(user => !user.bot).size;
                const inactiveUsers = totalUsers - activeUsers;

                this.sendJSON(res, {
                    labels: ['Active Users', 'Inactive Users'],
                    values: [activeUsers, inactiveUsers]
                });
                return;
            }

            if (path === '/stats/usersAndGuilds' && method === 'GET') {
                // Mock data - would need to implement historical tracking
                const currentDate = new Date();
                const mockData = {
                    users: Array.from({length: 7}, (_, i) => ({
                        date: new Date(currentDate.getTime() - i * 24 * 60 * 60 * 1000).toLocaleDateString(),
                        count: this.client.users.cache.size - Math.floor(Math.random() * 10)
                    })),
                    guilds: Array.from({length: 7}, (_, i) => ({
                        date: new Date(currentDate.getTime() - i * 24 * 60 * 60 * 1000).toLocaleDateString(),
                        count: this.client.guilds.cache.size - Math.floor(Math.random() * 2)
                    }))
                };

                this.sendJSON(res, mockData);
                return;
            }

            // Database endpoints (mock for now)
            if (path === '/database/backups' && method === 'GET') {
                this.sendJSON(res, []);
                return;
            }

            if (path === '/database/size' && method === 'GET') {
                this.sendJSON(res, { size: '0 MB' });
                return;
            }

            // 404 for unknown endpoints
            this.sendJSON(res, { error: 'Endpoint not found' }, 404);

        } catch (error) {
            console.error('API Error:', error);
            this.sendJSON(res, { error: 'Internal server error' }, 500);
        }
    }

    start() {
        return new Promise((resolve, reject) => {
            try {
                this.server.listen(this.port, () => {
                    console.log(`API Server running on port ${this.port}`);
                    console.log(`API Token: ${this.apiToken}`);
                    resolve(this.server);
                });
            } catch (error) {
                reject(error);
            }
        });
    }

    stop() {
        return new Promise((resolve) => {
            if (this.server) {
                this.server.close(() => {
                    console.log('API Server stopped');
                    resolve();
                });
            } else {
                resolve();
            }
        });
    }
}

module.exports = APIServer;
