const Discord = require('discord.js');
const { SlashCommandBuilder } = require('@discordjs/builders');

const Schema = require("../../database/models/birthday");

module.exports = {
    data: new SlashCommandBuilder()
        .setName('delete')
        .setDescription('Delete your birthday.'),
    async execute(client, interaction, args) {
        Schema.findOne({ Guild: interaction.guild.id, User: interaction.user.id }, async (err, data) => {
            if (!data) return client.errNormal({ 
                error: "No birthday found!",
                type: 'editreply' 
            }, interaction);

            Schema.findOneAndDelete({ Guild: interaction.guild.id, User: interaction.user.id }).then(() => {
                client.succNormal({ 
                    text: "Deleted your birthday", 
                    type: 'editreply' 
                }, interaction)
            })
        })
    },
};

 