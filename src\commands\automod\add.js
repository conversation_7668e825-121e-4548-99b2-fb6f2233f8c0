const Discord = require('discord.js');
const { SlashCommandBuilder } = require('@discordjs/builders');

const Schema = require("../../database/models/blacklist");

module.exports = {
    data: new SlashCommandBuilder()
        .setName('add')
        .setDescription('Add a word to the blacklist.')
        .addStringOption(option =>
            option.setName('word')
                .setDescription('The word to add to the blacklist.')
                .setRequired(true)),
    async execute(client, interaction, args) {
        const word = interaction.options.getString('word');

        Schema.findOne({ Guild: interaction.guild.id }, async (err, data) => {
            if (data) {
                if (data.Words.includes(word)) {
                    return client.errNormal({ 
                        error: `That word is already exists in the database!`,
                        type: 'editreply' 
                    }, interaction);
                }
                if(!data.Words) data.Words = [];
                data.Words.push(word);
                data.save();
            }
            else {
                new Schema({
                    Guild: interaction.guild.id,
                    Words: word
                }).save();
            }
        })

        client.succNormal({
            text: `Word is now blacklisted!`,
            fields: [
                {
                    name: `💬┆Word`,
                    value: `${word}`
                }
            ],
            type: 'editreply'
        }, interaction);
    },
};

 