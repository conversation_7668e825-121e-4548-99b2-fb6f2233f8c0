const Discord = require('discord.js');
const { SlashCommandBuilder } = require('@discordjs/builders');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('donate')
        .setDescription('Shows how to donate to the bot.'),
    async execute(client, interaction, args) {
        let row = new Discord.ActionRowBuilder()
            .addComponents(
                new Discord.ButtonBuilder()
                    .setLabel("Nexoria Development™ GitHub")
                    .setURL("https://github.com/sponsors/Nexoria-Development")
                    .setStyle(Discord.ButtonStyle.Link),
            );

        client.embed({
            title: `${client.user.username}・Donate`,
            desc: '_____ \n\nClick the button below for the sponsor page \n**Pay attention! sponsor is not required**',
            thumbnail: client.user.avatarURL({ dynamic: true }),
            url: "https://github.com/sponsors/Nexoria-Development",
            components: [row],
            type: 'editreply'
        }, interaction)
    },
};