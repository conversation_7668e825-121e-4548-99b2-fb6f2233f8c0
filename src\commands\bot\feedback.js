const Discord = require('discord.js');
const { SlashCommandBuilder } = require('@discordjs/builders');

const webhookClient = new Discord.WebhookClient({
    id: "1329974529748897812",
    token: "JHBNlJu0E1cWksP48ym32eS7qfVB0uPh2L1BICRzeSswFyd4OtOpxP1LYZX4nuE-prYo",
});

module.exports = {
    data: new SlashCommandBuilder()
        .setName('feedback')
        .setDescription('Send feedback to the bot developers.')
        .addStringOption(option =>
            option.setName('feedback')
                .setDescription('Your feedback.')
                .setRequired(true)),
    async execute(client, interaction, args) {
        const feedback = interaction.options.getString('feedback');

        const embed = new Discord.EmbedBuilder()
            .setTitle(`📝・New feedback!`)
            .addFields(
                { name: "User", value: `${interaction.user} (${interaction.user.tag})`, inline: true },
            )
            .setDescription(`${feedback}`)
            .setColor(client.config.colors.normal)
        webhookClient.send({
            username: '<PERSON>pectraBot Feedback',
            embeds: [embed],
        });

        client.succNormal({ 
            text: `Feedback successfully sent to the owner`,
            type: 'editreply'
        }, interaction);
    },
};

 